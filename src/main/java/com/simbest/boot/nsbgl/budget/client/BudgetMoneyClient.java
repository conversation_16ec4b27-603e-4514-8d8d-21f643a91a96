package com.simbest.boot.nsbgl.budget.client;

import com.cmcc.mss.msgheader.MsgHeader;
import com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OSBCEAMZCHQPageInquiryAssetSrv;
import com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.InputParameters;
import com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OutputParameters;
import com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.msgheader.MSGHEADER;
import com.cmcc.mss.sb_fi_bms_pageinquirywxprjmapbgtdptinfosrv.*;
import com.simbest.boot.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.math.BigDecimal;
import java.util.Date;
import java.util.GregorianCalendar;


/**
 * 预算管理数据同步客户端
 *
 * @Auther: ztz
 * @Date: 2020/4/16 11:09
 * @Description:
 */
@Slf4j
@Component
@DependsOn(value = {"budgetCxfConfiguration"})
public class BudgetMoneyClient {

    @Autowired
    private SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrv budGetService;

    @Autowired
    private OSBCEAMZCHQPageInquiryAssetSrv assetService;

    private static BudgetMoneyClient client;

    @PostConstruct
    public void init() {
        client = this;
        client.budGetService = this.budGetService;
        client.assetService = this.assetService;
    }

    public static SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvResponse searchBudget(BigDecimal currentPage, BigDecimal pageSize, BigDecimal totalRecord) {
        //创建webservice请求对象
        SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvRequest payload = new SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvRequest();
        //创建请求头msgheader
        MsgHeader msgHeader = new MsgHeader();
        msgHeader.setSOURCESYSTEMID("nsbgl");
        msgHeader.setSOURCESYSTEMNAME("设备维修管理");
        msgHeader.setUSERID("zhouwenb");
        msgHeader.setUSERNAME("周文博");
//        msgHeader.setUSERID("daierjuan");
//        msgHeader.setUSERNAME("代二娟");
        msgHeader.setCURRENTPAGE(currentPage);
        msgHeader.setPAGESIZE(pageSize);
        msgHeader.setTOTALRECORD(totalRecord);
       msgHeader.setPROVINCECODE("4772276805467173986");
        //获取当前时间
        //时间格式：2021-11-23T09:44:29.133+08:00
        msgHeader.setSUBMITDATE(convertToXMLGregorianCalendar(new Date()));
        payload.setMsgHeader(msgHeader);
        //设置查询年份
        payload.setBUDGETYEAR(new BigDecimal(DateUtil.getCurrYear()));
       payload.setPROJECTNUM("P302521220023");
        payload.setBUDGETYEAR(new BigDecimal(2022));
        return client.budGetService.process(payload);
    }





    public static SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvResponse manualsearchBudget( BigDecimal currentPage, BigDecimal pageSize, BigDecimal totalRecord,String preBudgetNum) {
        //创建webservice请求对象
        SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvRequest payload = new SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvRequest();
        //创建请求头msgheader
        MsgHeader msgHeader = new MsgHeader();
        msgHeader.setSOURCESYSTEMID("nsbgl");
        msgHeader.setSOURCESYSTEMNAME("设备维修管理");
        msgHeader.setUSERID("zhouwenbo");
        msgHeader.setUSERNAME("周文博");
//        msgHeader.setUSERID("daierjuan");
//        msgHeader.setUSERNAME("代二娟");
        msgHeader.setCURRENTPAGE(currentPage);
        msgHeader.setPAGESIZE(pageSize);
        msgHeader.setTOTALRECORD(totalRecord);
      //  msgHeader.setPROVINCECODE("4772423818420402766");
        //获取当前时间
        //时间格式：2021-11-23T09:44:29.133+08:00
        msgHeader.setSUBMITDATE(convertToXMLGregorianCalendar(new Date()));
        payload.setMsgHeader(msgHeader);
        //设置查询年份
        payload.setBUDGETYEAR(new BigDecimal(DateUtil.getCurrYear()));
        payload.setPROJECTNUM(preBudgetNum);
        System.out.println(payload);
        return client.budGetService.process(payload);
    }


    /**
     * 用来把Date类型转为XMLGregorianCalendar
     *
     * @param date
     * @return
     */
    public static XMLGregorianCalendar convertToXMLGregorianCalendar(Date date) {

        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(date);
        XMLGregorianCalendar gc = null;
        try {
            gc = DatatypeFactory.newInstance().newXMLGregorianCalendar(cal);
        } catch (Exception e) {

            e.printStackTrace();
        }
        return gc;
    }





    //资管数据同步

    /**
     * 查询资产信息（分页）
     * @param currentPage 当前页
     * @param pageSize 页大小
     * @param totalRecord 总记录数
     * @param provinceCode 省份代码
     * @return 资产查询响应
     */
    public static OutputParameters searchAsset(BigDecimal currentPage, BigDecimal pageSize,
                                             BigDecimal totalRecord, String provinceCode) {
        return searchAsset(currentPage, pageSize, totalRecord, provinceCode, null, null,
                          null, null, null, null, null, null, null, null);
    }

    /**
     * 查询资产信息（分页）- 完整参数版本
     * @param currentPage 当前页
     * @param pageSize 页大小
     * @param totalRecord 总记录数
     * @param provinceCode 省份代码
     * @param dutyDeptCode 责任部门代码
     * @param dutyCode 责任人代码
     * @param assetCategory 资产类别
     * @param assetKind 资产种类
     * @param barCode 条码
     * @param addressCode 地址代码
     * @param statusName 状态名称
     * @param isLeave 是否离职
     * @param lastUpdateStart 最后更新开始时间
     * @param lastUpdateEnd 最后更新结束时间
     * @return 资产查询响应
     */
    public static OutputParameters searchAsset(BigDecimal currentPage, BigDecimal pageSize,
                                             BigDecimal totalRecord, String provinceCode,
                                             String dutyDeptCode, String dutyCode, String assetCategory,
                                             String assetKind, String barCode, String addressCode,
                                             String statusName, String isLeave,
                                             XMLGregorianCalendar lastUpdateStart, XMLGregorianCalendar lastUpdateEnd) {
        //创建webservice请求对象
        InputParameters payload = new InputParameters();

        //创建请求头msgheader
        MSGHEADER msgHeader = new MSGHEADER();
        msgHeader.setSOURCESYSTEMID("nsbgl");
        msgHeader.setSOURCESYSTEMNAME("设备维修管理");
        msgHeader.setTOKEN(""); // 根据实际需要设置token
        msgHeader.setUSERID(new BigDecimal("1")); // 根据实际需要设置用户ID
        msgHeader.setUSERNAME("系统管理员");
        msgHeader.setUSERPASSWD(""); // 根据实际需要设置密码
        msgHeader.setCURRENTPAGE(currentPage);
        msgHeader.setPAGESIZE(pageSize);
        msgHeader.setTOTALRECORD(totalRecord);
        msgHeader.setPROVINCECODE(provinceCode);
        //获取当前时间
        msgHeader.setSUBMITDATE(convertToXMLGregorianCalendar(new Date()));

        payload.setMSGHEADER(msgHeader);

        //设置查询参数
        payload.setPROVINCECODE(provinceCode);
        payload.setDUTYDEPTCODE(dutyDeptCode);
        payload.setDUTYCODE(dutyCode);
        payload.setASSETCATEGORY(assetCategory);
        payload.setASSETKIND(assetKind);
        payload.setBARCODE(barCode);
        payload.setADDRESSCODE(addressCode);
        payload.setSTATUSNAME(statusName);
        payload.setISLEAVE(isLeave);
        payload.setLASTUPDATESTART(lastUpdateStart);
        payload.setLASTUPDATEEND(lastUpdateEnd);

        log.info("资产查询请求参数: {}", payload);

        try {
            OutputParameters response = client.assetService.process(payload);
            log.info("资产查询响应: {}", response);
            return response;
        } catch (Exception e) {
            log.error("资产查询异常", e);
            throw new RuntimeException("资产查询失败: " + e.getMessage(), e);
        }
    }








































}
