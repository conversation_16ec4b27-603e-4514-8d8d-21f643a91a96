package com.simbest.boot.nsbgl.budget.client;

import com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OSBCEAMZCHQPageInquiryAssetSrv;
import com.cmcc.mss.sb_fi_bms_pageinquirywxprjmapbgtdptinfosrv.SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrv;
import com.cmcc.mss.sb_oa_oa_importtodoopenlistinfosrv.SBOAOAImportToDoOpenListInfoSrv;
import lombok.Getter;
import lombok.Setter;
import org.apache.cxf.bus.spring.SpringBus;
import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Auther: ztz
 * @Date: 2020/4/16 11:10
 * @Description:
 */
@Configuration
@ConfigurationProperties(prefix = "cxf")
public class BudgetCxfConfiguration {

    @Setter
    @Getter
    private String searchBudgetUrl;

    @Setter
    @Getter
    private String searchEqumentUrl;

    /**
     * 定义获取预算接口
     * @param bus
     * @return
     */
    @Bean
    public SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrv BudgetSrv(SpringBus bus) {
        JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
        factory.setServiceClass(SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrv.class);
        factory.setAddress(searchBudgetUrl); //接口平台远程地址
        factory.setBus(bus);
        return factory.create(SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrv.class);
    }


    @Bean
    public OSBCEAMZCHQPageInquiryAssetSrv assetSrv(SpringBus bus) {
        JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
        factory.setServiceClass(OSBCEAMZCHQPageInquiryAssetSrv.class);
        factory.setAddress(searchEqumentUrl); //接口平台远程地址
        factory.setBus(bus);
        return factory.create(OSBCEAMZCHQPageInquiryAssetSrv.class);
    }
}
