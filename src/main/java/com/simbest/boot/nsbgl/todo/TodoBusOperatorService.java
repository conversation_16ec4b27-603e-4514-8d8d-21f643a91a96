package com.simbest.boot.nsbgl.todo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.enums.ToDoEnum;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.process.bussiness.service.impl.ActBusinessStatusService;
import com.simbest.boot.bps.process.listener.model.WFNotificationInstModel;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.cmcc.hq.clients.HqTodoRestClient;
import com.simbest.boot.cmcc.hq.model.*;
import com.simbest.boot.cmcc.hq.service.IUsernameAccountService;
import com.simbest.boot.cmcc.hqlog.service.ISySHqTodoInfoService;
import com.simbest.boot.cmcc.nmsg.MsgPostOperatorService;
import com.simbest.boot.nsbgl.mainbills.model.UsPmInstence;
import com.simbest.boot.nsbgl.todo.model.UsTodoModel;
import com.simbest.boot.nsbgl.todo.repository.UsTodoModelRepository;
import com.simbest.boot.nsbgl.todo.service.IUsTodoModelService;
import com.simbest.boot.nsbgl.util.BpsConfig;
import com.simbest.boot.nsbgl.util.Constants;
import com.simbest.boot.nsbgl.util.SMSTool;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import com.simbest.boot.wf.process.service.IWorkItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <strong>Title : TodoBusOperatorService</strong><br>
 * <strong>Description : 统一代办业务操作</strong><br>
 * <strong>Create on : $date$</strong><br>
 * <strong>Modify on : $date$</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@Service
@SuppressWarnings("All")
public class TodoBusOperatorService extends LogicService<UsTodoModel, String> {

    private final String[] msgSendUsers = new String[]{"oalijianwu","oazhangqianfeng","oasunhaoran","oawuxingyu","sjbg"};

    @Autowired
    private MsgPostOperatorService msgPostOperatorService;

    private UsTodoModelRepository usTodoModelRepository;

    @Autowired
    private IUsTodoModelService usTodoModelService;

    @Autowired
    private BpsConfig bpsConfig;

    @Autowired
    private SMSTool smsTool;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private IWorkItemService  workItemService;

    @Autowired
    private IUsernameAccountService usernameAccountService;

    @Autowired
    private HqTodoRestClient todoRestClient;

    @Value("${spring.profiles.active}")
    private String profilesActive;

    @Value("${uni.todo.mobile.address}")
    private String mobileTodoAddress;

    private  ZoneId zone = ZoneId.of("Asia/Shanghai");

    @Autowired
    private ISySHqTodoInfoService sySHqTodoInfoService;

    public static final Map<String, String> todoHtml;

    static {
        Map<String, String> todoHtmlTmp = Maps.newConcurrentMap();
        todoHtmlTmp.put(Constants.FLOW_TYPE_A, "/html/equipment/equipmentPushList.html");// 设备信息新增流程(统一推送)
        todoHtmlTmp.put(Constants.FLOW_TYPE_B, "/html/equipment/equipmentNoPushList.html");// 设备信息新增流程(手动起草)
        todoHtmlTmp.put(Constants.FLOW_TYPE_C, "/html/apply/applicationModifyForm.html");// 设备信息变更流程
        todoHtmlTmp.put(Constants.FLOW_TYPE_D, "/html/apply/applicationMaintainForm.html");// 设备维修申请流程
        todoHtmlTmp.put(Constants.FLOW_TYPE_F, "/html/apply/applicationConsumeForm.html");// 耗材领用申请流程
        todoHtml = todoHtmlTmp;
    }

    @Autowired
    private ActBusinessStatusService actBusinessStatusService;
    @Autowired
    private UumsSysAppApi uumsSysAppApi;

    @Autowired
    public TodoBusOperatorService(UsTodoModelRepository repository) {
        super(repository);
        this.usTodoModelRepository = repository;
    }

    /**
     * 推送统一代办
     *
     * @param businessStatus 业务状态操作对象
     * @param userName       审批人
     */
    public void openTodo(ActBusinessStatus businessStatus, String userName) {
        log.debug( "TodoBusOperatorService>>>>>>>openTodo>>>>>调用接口平台推送统一代办开始");
        String oaHtmlUrl = null;
        String urlParams = null;

        String mobileUrl = null;
        SimpleHqTodo hqTodo = null;
        Boolean sendFalg = false;
        log.debug( "businessStatus>>>>>>>> {}" , JacksonUtils.obj2json(businessStatus));
        try {
            //截取到数字，代表流程类型
            String pmInsType = businessStatus.getReceiptCode().substring(0,1);
            //pc办理页地址
            oaHtmlUrl = bpsConfig.hostPost + "/" + Constants.APP_CODE + todoHtml.get(pmInsType);
            //手机办公办理页地址
            mobileUrl = mobileTodoAddress + "/hamoa/nsbgl" ;
            //pc路径后面带的参数，即url ?后面的数据
            urlParams = "?type=task&location=" + businessStatus.getActivityDefId()
                    + "&pmInsId=" + businessStatus.getReceiptCode()
                    + "&processInstId=" + businessStatus.getProcessInstId()
                    + "&processDefName=" + businessStatus.getProcessDefName()
                    + "&workItemId=" + businessStatus.getWorkItemId()
                    + "&pmInsType=" + pmInsType + "&name=auditVal&appcode=nsbgl&from=oa"
                    + "&clientId=" + Constants.APP_ID + "&srcapp=jtmoa&itemType=0";
                if("C".equals(pmInsType)){
                    urlParams = urlParams + "&showFlag=false";
                }else{
                    urlParams = urlParams + "&showFlag=true";
                }
            hqTodo = SimpleHqTodo.builder()
                    .appId(Constants.APP_ID)
                    .itemCreateTime( StrUtil.isNotEmpty(businessStatus.getSpace1()) ? Long.parseLong(businessStatus.getSpace1()) : System.currentTimeMillis())
                    .appName(Constants.APP_NAME)
                    .itemId(String.valueOf(businessStatus.getWorkItemId()))
                    .itemType("0") //0：待办 ， 1：待阅 ， 2：已办 ， 3：已阅
                    .lastUpdateTime(System.currentTimeMillis())
                    .itemUrl(oaHtmlUrl + urlParams)
                    .itemMOAUrl(mobileUrl + urlParams)
                    .processInstanceId(String.valueOf(businessStatus.getProcessInstId()))
                    .build();
            //获取办理人信息
            Set<String> usernameSet = Sets.newHashSet();
            Map<String, String> userMap = Maps.newHashMap();
            //办理人
            usernameSet.add(userName);
            //起草人
            usernameSet.add(businessStatus.getCreateUserCode());
            //上一步办理人
            if (StrUtil.isNotEmpty(businessStatus.getPreviousAssistant())) {
                usernameSet.add(businessStatus.getPreviousAssistant());
            }
            //环节信息
            String activityDefId = StrUtil.isNotEmpty(businessStatus.getActivityDefId()) ? businessStatus.getActivityDefId() : "approval_location";
            String activityDefName =  StrUtil.isNotEmpty(businessStatus.getActivityInstName()) ? businessStatus.getActivityInstName() : "业务审批";

            Map<String, HqUserInfo> userInfoMap = findByUsernames(usernameSet);
            Assert.notEmpty(userInfoMap , "查询人员信息失败！");
            HqUserInfo receiver = userInfoMap.get(userName);
            Assert.notNull(receiver , "当前办理人信息查询失败！");
            hqTodo.setReceiver(receiver);
            hqTodo.setReceiverUserId(receiver.getUserId());

            HqUserInfo drafter = userInfoMap.get(businessStatus.getCreateUserCode());
            Assert.notNull(drafter , "获取起草人失败！");

            Boolean isStart = StringUtils.isNotEmpty(businessStatus.getPreviousAssistant()) ;

            //获取流程实例信息
            ProcessDef processDef = ProcessDef.builder()
                    .activityId(activityDefId)
                    .activityName(activityDefName)
                    .processTypeId(businessStatus.getProcessDefName())
                    .processTypeName(Constants.APP_NAME)
                    .definitionId(String.valueOf(businessStatus.getProcessInstId()))
                    .definitionName(Constants.APP_NAME)
                    .instanceStartTime(businessStatus.getCreateTime().atZone(zone).toInstant().toEpochMilli())
                    .instanceCreateUser(drafter.getUserId())
                    .startFlag( isStart ? "false" : "true")
                    .endFlag("false")
                    .build();
            hqTodo.setProcessDef(processDef);

            //封装上一办理人信息
            HqUserInfo lastHandleuser = null;
            if (StrUtil.isNotEmpty(businessStatus.getPreviousAssistant()) ) {
                lastHandleuser = userInfoMap.get(businessStatus.getPreviousAssistant());
            }
            if (null == lastHandleuser) {
                lastHandleuser = HqUserInfo.builder().userId("-").userName("-").idType("0").build();
            }
            hqTodo.setLastHandler(Collections.singletonList(lastHandleuser));

            //封装文种信息
            DocInfo docInfo = DocInfo.builder()
                    .docInsId(String.valueOf(businessStatus.getWorkItemId()))
                    .docState("1")
                    .itemTitle(businessStatus.getReceiptTitle())
                    .createDept(businessStatus.getCreateOrgName())
                    .docCreateTime(System.currentTimeMillis())
                    .docTypeId(Constants.APP_ID)
                    .docTypeName(Constants.APP_NAME)
                    .isMyDoc(StrUtil.equals(userName, businessStatus.getCreateUserCode()) ? "Y" : "N")
                    .drafter(drafter)
                    .build();
            hqTodo.setDocInfo(docInfo);
            log.warn("推送统一待办数据：{}" , JacksonUtils.obj2json(hqTodo));
            JsonResponse jsonResponse = todoRestClient.pushHqTodoSimple(hqTodo);
            if (jsonResponse.getErrcode() != 0) {
                log.error("推送统一待办待办失败！");
            } else {
                sendFalg = Boolean.TRUE;
            }
        } catch (Exception e) {
            Exceptions.printException( e );
            log.error("TodoBusOperatorService>>>>>>>openTodo>>>>>调用推送统一待办接口异常");
            //发送推送统一待办异常短信
            String msgContent = Constants.APP_NAME.concat(":【").concat(businessStatus.getReceiptTitle()).concat("】").concat("，待办人：【").concat(userName).concat("】").concat("，调用接口平台推送统一代办异常");
            smsTool.sendMsgUtil(msgContent);
        }
        sySHqTodoInfoService.saveHqTodoSendInfo(hqTodo , sendFalg);
        log.warn("TodoBusOperatorService>>>>>>>openTodo>>>>>调用接口平台推送统一代办结束");
    }

    /**
     * 核销统一代办
     *
     * @param businessStatus 业务状态操作对象
     * @param userName       审批人
     */
    public void closeTodo(ActBusinessStatus businessStatus, String userName) {
        log.warn("TodoBusOperatorService>>>>>>>closeTodo>>>>>调用接口平台核销统一代办开始");
        Boolean sendFalg = false;
        SimpleHqTodo hqTodo= null;
        log.warn("businessStatus>>>>>>>>【{}】",JacksonUtils.obj2json(businessStatus));
        try {
            //截取到数字，代表流程类型
            String pmInsType = businessStatus.getReceiptCode().substring(0,1);
            //pc办理页地址
            String oaHtmlUrl = bpsConfig.hostPost + "/" + Constants.APP_CODE + todoHtml.get(pmInsType);
            //手机办公办理页地址
            String mobileUrl = mobileTodoAddress + "/hamoa/nsbgl" ;
            //代办回调路径后面带的参数，即url ?后面的数据
            String urlParams = "?type=join&location=" + businessStatus.getActivityDefId()
                    + "&pmInsId=" + businessStatus.getReceiptCode()
                    + "&processInstId=" + businessStatus.getProcessInstId()
                    + "&processDefName=" + businessStatus.getProcessDefName()
                    + "&workItemId=" + businessStatus.getWorkItemId()
                    + "&pmInsType=" + pmInsType + "&name=auditVal&appcode=nsbgl&from=oa"
                    + "&clientId=" + Constants.APP_ID + "&srcapp=jtmoa&itemType=2";

            if("C".equals(pmInsType)){
                urlParams = urlParams + "&showFlag=false";
            }else{
                urlParams = urlParams + "&showFlag=true";
            }
            Set<String> userSet = Sets.newHashSet();
            userSet.add(userName);
            Map<String, HqUserInfo> userInfoMap = this.findByUsernames(userSet);
            HqUserInfo userInfo = userInfoMap.get(userName);
            Assert.notNull(userInfo , "获取办理人信息失败！");
            hqTodo = SimpleHqTodo.builder()
                    .appId(Constants.APP_ID)
                    .appName(Constants.APP_NAME)
                    .itemUrl(oaHtmlUrl + urlParams)
                    .itemMOAUrl(mobileUrl + urlParams)
                    .itemId(String.valueOf(businessStatus.getWorkItemId()))
                    .itemType("2")//0：待办 ， 1：待阅 ， 2：已办 ， 3：已阅
                    .lastUpdateTime(System.currentTimeMillis())
                    .receiverUserId(userInfo.getUserId())
                    .receiver(userInfo)
                    .build();
            JsonResponse jsonResponse = todoRestClient.updateStatusTodo(hqTodo);
            if (jsonResponse.getErrcode() != 0) {
                log.error("推送统一待办待办失败！");
            } else {
                sendFalg = Boolean.TRUE;
            }
        } catch (Exception e) {
            Exceptions.printException( e );
            log.error("TodoBusOperatorService>>>>>>>openTodo>>>>>调用核销统一待办接口异常");
            //发送推送统一待办异常短信
            String msgContent = Constants.APP_NAME.concat(":【").concat(businessStatus.getReceiptTitle()).concat("】").concat("，待办人：【").concat(userName).concat("】").concat("，调用接口平台核销统一代办异常");;
            smsTool.sendMsgUtil(msgContent);
        }
        sySHqTodoInfoService.saveHqTodoCancelInfo(hqTodo , sendFalg);
        log.warn("TodoBusOperatorService>>>>>>>closeTodo>>>>>调用接口平台核销统一代办结束");
    }


    /*
     *推送待阅通知到统一待办库
     */
    public void openTodoToRead(UsPmInstence usPmInstence, String copyNextUser, String copyNextUserNames, WFNotificationInstModel wfNotificationInstModel) {
        log.warn("TodoBusOperatorService>>>>>>>openTodoToRead>>>>>调用接口平台推送待阅待办开始");
        String oaHtmlUrl = null;
        String mobileUrl = null;
        String urlParams = null;
        Boolean sendFalg = false;
        ActBusinessStatus businessStatus = null;
        SimpleHqTodo hqTodo = null;
        String wfId = null;
        boolean isPostMsg = false;
        boolean isTodoFlag = false;
        try {
            SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode(Constants.APP_CODE, wfNotificationInstModel.getSendUser());
            if (simpleApp != null) {
                isTodoFlag = simpleApp.getTodoOpen();
                isPostMsg = simpleApp.getIsSendMsg();
            }

            if (isTodoFlag) {
                businessStatus = actBusinessStatusService.getByProcessInst(wfNotificationInstModel.getProcessInstId());
                wfId = wfNotificationInstModel.getId();
                businessStatus.setActivityDefId(wfNotificationInstModel.getNextActivityDefId());
                log.warn("businessStatus>>>>>>>>" + businessStatus.toString());
                String pmInsType = businessStatus.getReceiptCode().replaceAll("[^(A-Za-z)]", "");
                //pc办理页地址
                oaHtmlUrl = bpsConfig.hostPost + "/" + Constants.APP_CODE + todoHtml.get(pmInsType);
                //手机办公办理页地址
                mobileUrl = mobileTodoAddress + "/hamoa/nsbgl" ;
                //代办回调路径后面带的参数，即url ?后面的数据
                urlParams = "?type=toRead&location=" + wfNotificationInstModel.getNextActivityDefId() +
                        "&processInstId=" + businessStatus.getProcessInstId() +
                        "&pmInsType=" + pmInsType +
                        "&processDefName=" + businessStatus.getProcessDefName()
                        + "&notificationId=" + wfNotificationInstModel.getId()
                        + "&name=auditVal&appcode=" + Constants.APP_CODE
                        + "&from=oa&pmInsId=" + businessStatus.getReceiptCode()
                        + "&clientId=" + Constants.APP_ID + "&srcapp=jtmoa&itemType=1" ;

                hqTodo = SimpleHqTodo.builder()
                        .appId(Constants.APP_ID)  //应用id
                        .itemCreateTime( StrUtil.isNotEmpty(businessStatus.getSpace1()) ? Long.parseLong(businessStatus.getSpace1()) : System.currentTimeMillis())
                        .appName(Constants.APP_NAME)
                        .itemId(wfNotificationInstModel.getId())
                        .itemType("1") //0：待办 ， 1：待阅 ， 2：已办 ， 3：已阅
                        .lastUpdateTime(System.currentTimeMillis())
                        .itemUrl(oaHtmlUrl + urlParams)
                        .itemMOAUrl(mobileUrl + urlParams + "&showFlag=true")
                        .processInstanceId(wfNotificationInstModel.getId())
                        .build();
                //获取办理人信息
                Set<String> usernameSet = Sets.newHashSet();
                Map<String, String> userMap = Maps.newHashMap();
                //办理人/上一步办理人
                usernameSet.add(wfNotificationInstModel.getSendUser());
                usernameSet.add(wfNotificationInstModel.getRecipient());
                usernameSet.add(businessStatus.getCreateUserCode());

                String activityDefId = String.valueOf(wfNotificationInstModel.getActivityInstId());
                String activityDefName = wfNotificationInstModel.getActivityInstName();
                if (StrUtil.isEmpty(activityDefId) || StrUtil.isEmpty(activityDefName)) {
                    activityDefId = Constants.APP_CODE + "_copy";
                    activityDefName = "待阅";
                }
                Map<String, HqUserInfo> userInfoMap = findByUsernames(usernameSet);
                Assert.notEmpty(userInfoMap , "查询人员信息失败！");
                HqUserInfo receiver = userInfoMap.get(wfNotificationInstModel.getRecipient());
                Assert.notNull(receiver , "当前办理人信息查询失败！");
                hqTodo.setReceiver(receiver);
                hqTodo.setReceiverUserId(receiver.getUserId());

                HqUserInfo drafter = userInfoMap.get(businessStatus.getCreateUserCode());
                Assert.notNull(drafter , "获取起草人失败！");

                //获取流程实例信息
                ProcessDef processDef = ProcessDef.builder()
                        .activityId(activityDefId)
                        .activityName(activityDefName)
                        .processTypeId(businessStatus.getProcessDefName())
                        .processTypeName(Constants.APP_NAME)
                        .definitionId(String.valueOf(businessStatus.getProcessInstId()))
                        .definitionName(Constants.APP_NAME)
                        .instanceStartTime(businessStatus.getCreateTime().atZone(zone).toInstant().toEpochMilli())
                        .instanceCreateUser(drafter.getUserId())
                        .startFlag( "false" )
                        .endFlag("false")
                        .build();
                hqTodo.setProcessDef(processDef);

                //封装上一办理人信息
                HqUserInfo lastHandleuser = userInfoMap.get(wfNotificationInstModel.getSendUser());
                if (null == lastHandleuser) {
                    lastHandleuser = HqUserInfo.builder().userId("-").userName("-").idType("0").build();
                }
                hqTodo.setLastHandler(Collections.singletonList(lastHandleuser));

                //封装文种信息
                DocInfo docInfo = DocInfo.builder()
                        .docInsId(String.valueOf(businessStatus.getWorkItemId()))
                        .docState("1")
                        .itemTitle(businessStatus.getReceiptTitle())
                        .createDept(businessStatus.getCreateOrgName())
                        .docCreateTime(System.currentTimeMillis())
                        .docTypeId(Constants.APP_ID)
                        .docTypeName(Constants.APP_NAME)
                        .isMyDoc(StrUtil.equals(wfNotificationInstModel.getSendUser(), businessStatus.getCreateUserCode()) ? "Y" : "N")
                        .drafter(drafter)
                        .build();
                hqTodo.setDocInfo(docInfo);
                log.warn("推送统一待办数据：{}" , JacksonUtils.obj2json(hqTodo));
                JsonResponse jsonResponse = todoRestClient.pushHqTodoSimple(hqTodo);
                if (jsonResponse.getErrcode() != 0) {
                     log.error("推送统一待办待办失败！");
                } else {
                    sendFalg = Boolean.TRUE;
                }
            }
            /**如果开关开启且待发列表不为空则发短信**/
            if (isPostMsg) {
                Boolean isPostMsgOK = false;
                if (StringUtils.isNotEmpty(copyNextUser)) {
                    /**准备审批短信模板数据**/
                    Map<String, String> paramMap = Maps.newHashMap();
                    paramMap.put("appName", Constants.APP_NAME);
                    paramMap.put("fromUser", businessStatus.getPreviousAssistantName());
                    paramMap.put("itemSubject", businessStatus.getReceiptTitle());
                    String msg = "${appName}:您收到${fromUser}向您发送的[${itemSubject}]的待阅工单，请及时处理。"
                            .replace("${appName}", Constants.APP_NAME).replace("${fromUser}", wfNotificationInstModel.getSendUserName())
                            .replace("${itemSubject}", wfNotificationInstModel.getReceiptTitle());
                    ;
                    isPostMsgOK = msgPostOperatorService.postMsg(TodoOpenServiceImpl.readyParams(copyNextUser, msg));
                    log.warn("待阅短信发送状态【{}】", isPostMsgOK);
                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            log.error("TodoBusOperatorService>>>>>>>openTodoToRead>>>>>调用推送统一待阅待办接口异常");
            //发送推送统一待办异常短信
            String msgContent = Constants.APP_NAME.concat(":【").concat(businessStatus.getReceiptTitle()).concat("】").concat("，待阅人：【").concat(copyNextUser).concat("】").concat("，调用接口平台推送统一待办异常");
            smsTool.sendMsgUtil(msgContent);
        }
        sySHqTodoInfoService.saveHqTodoSendInfo(hqTodo , sendFalg);
        log.warn("TodoBusOperatorService>>>>>>>openTodo>>>>>调用接口平台推送待阅待办结束");
    }

    /**
     * 核销待阅
     */
    public void closeTodoDoRead(UsPmInstence usPmInstence, WFNotificationInstModel wfNotificationInstModel) {
        log.warn("TodoBusOperatorService>>>>>>>closeTodoDoRead>>>>>调用接口平台核销待阅待办开始");
        Boolean sendFalg = false;
        ActBusinessStatus businessStatus = null;
        SimpleHqTodo hqTodo = null;
        String wfId = null;
        try {
            businessStatus = actBusinessStatusService.getByProcessInst(wfNotificationInstModel.getProcessInstId());;
            wfId = wfNotificationInstModel.getId();
            businessStatus.setActivityDefId(wfNotificationInstModel.getNextActivityDefId());
            String pmInsType = businessStatus.getReceiptCode().replaceAll("[^(A-Za-z)]", "");
            //pc办理页地址
            String oaHtmlUrl = bpsConfig.hostPost + "/" + Constants.APP_CODE + todoHtml.get(pmInsType);
            //手机办公办理页地址
            String mobileUrl = mobileTodoAddress + "/hamoa/nsbgl" ;
            //代办回调路径后面带的参数，即url ?后面的数据
            String urlParams = "?type=doRead&location=" + wfNotificationInstModel.getNextActivityDefId() +
                    "&processInstId=" + businessStatus.getProcessInstId() +
                    "&pmInsType=" + pmInsType +
                    "&processDefName=" + businessStatus.getProcessDefName()
                    + "&notificationId=" + wfNotificationInstModel.getId()
                    + "&name=auditVal&showFlag=true&appcode=" + Constants.APP_CODE
                    + "&from=oa&pmInsId=" + businessStatus.getReceiptCode()
                    + "&clientId=" + Constants.APP_ID + "&srcapp=jtmoa&itemType=3" ;

            Set<String> userSet = Sets.newHashSet();
            userSet.add(wfNotificationInstModel.getRecipient());
            Map<String, HqUserInfo> userInfoMap = this.findByUsernames(userSet);
            HqUserInfo userInfo = userInfoMap.get(wfNotificationInstModel.getRecipient());
            Assert.notNull(userInfo , "获取办理人信息失败！");
            hqTodo = SimpleHqTodo.builder()
                    .appId(Constants.APP_ID)
                    .appName(Constants.APP_NAME)
                    .itemUrl(oaHtmlUrl + urlParams)
                    .itemMOAUrl(mobileUrl + urlParams + "&showFlag=true")
                    .itemId(wfNotificationInstModel.getId())
                    .itemType("3")//0：待办 ， 1：待阅 ， 2：已办 ， 3：已阅
                    .lastUpdateTime(System.currentTimeMillis())
                    .receiverUserId(userInfo.getUserId())
                    .receiver(userInfo)
                    .build();
            JsonResponse jsonResponse = todoRestClient.updateStatusTodo(hqTodo);
            if (jsonResponse.getErrcode() != 0) {
                log.error("推送统一待办待办失败！");
            } else {
                sendFalg = Boolean.TRUE;
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            log.error("TodoBusOperatorService>>>>>>>closeTodoDoRead>>>>>调用核销统一待办接口异常");
            //发送推送统一待办异常短信
            String msgContent = Constants.APP_NAME.concat(":【").concat(businessStatus.getReceiptTitle()).concat("】").concat("，待办人：【").concat(wfNotificationInstModel.getRecipient()).concat("】").concat("，调用接口平台核销统一代办异常");
            smsTool.sendMsgUtil(msgContent);
        }
        sySHqTodoInfoService.saveHqTodoCancelInfo(hqTodo , sendFalg);
        log.warn("TodoBusOperatorService>>>>>>>closeTodoDoRead>>>>>调用接口平台核销待阅待办结束");
    }


    @SuppressWarnings("unused")
    private UsTodoModel saveTodoModel(ActBusinessStatus businessStatus,String todoUser,String oaHtmlUrl,String urlParams,Boolean sendFalg,String sysId,String sysName){
        UsTodoModel usTodoModel = new UsTodoModel();
        try {
            usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
            usTodoModel.setWorkItemId(StrUtil.toString(businessStatus.getWorkItemId()));
            Specification<UsTodoModel> usTodoModelSpecification = usTodoModelService.getSpecification(usTodoModel);
            usTodoModel = usTodoModelService.findOne(usTodoModelSpecification);
        }catch (Exception e){
            Exceptions.printException( e );
        }finally {
            if (ObjectUtil.isEmpty(usTodoModel)) {
                usTodoModel = new UsTodoModel();
                usTodoModel.setWorkItemId(businessStatus.getWorkItemId().toString());
                usTodoModel.setActivityDefId(businessStatus.getActivityDefId());
                usTodoModel.setProcessInstanceId(StrUtil.toString(businessStatus.getProcessInstId()));
                usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
                usTodoModel.setBusinessStatusId(businessStatus.getId());
                usTodoModel.setProcessDefId(StrUtil.toString(businessStatus.getProcessDefId()));
                usTodoModel.setCreator(businessStatus.getCreateUserCode());
                usTodoModel.setCreatedTime(businessStatus.getCreateTime());
                usTodoModel.setModifiedTime(businessStatus.getEndTime());
                usTodoModel.setModifier(todoUser);
                usTodoModel.setUserName(todoUser);
                usTodoModel.setSender(todoUser);
                usTodoModel.setTitle(businessStatus.getReceiptTitle());
                usTodoModel.setTypeStatus(ToDoEnum.open.getValue());
                usTodoModel.setOaHtmlUrl(oaHtmlUrl);
                usTodoModel.setUrlParams(urlParams);
                usTodoModel.setWorkFlag(true);
                usTodoModel.setEnabled(true);
                usTodoModel.setSendFlag(sendFalg);
                usTodoModel.setSendDate(businessStatus.getEndTime());
                usTodoModel.setSysId(sysId);
                usTodoModel.setSysName(sysName);
                usTodoModel = usTodoModelService.savaLocalTodoData(usTodoModel);
            }
        }
        log.warn("usTodoModelService>>>insert>>>>>【{}】", JacksonUtils.obj2json(usTodoModel));
        return usTodoModel;
    }

    @SuppressWarnings("unused")
    private UsTodoModel closeTodoModel(ActBusinessStatus businessStatus,String todoUser,Boolean sendFalg){
        UsTodoModel usTodoModel = new UsTodoModel();
        try {
            usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
            usTodoModel.setSender(todoUser);
            usTodoModel.setWorkFlag(true);
            usTodoModel.setWorkItemId(StrUtil.toString(businessStatus.getWorkItemId()));
            Specification<UsTodoModel> usTodoModelSpecification = usTodoModelService.getSpecification(usTodoModel);
            usTodoModel = usTodoModelService.findOne(usTodoModelSpecification);
        }catch (Exception e){
            Exceptions.printException( e );
        }finally {
            if (ObjectUtil.isNotEmpty(usTodoModel)){
                usTodoModel.setWorkFlag(false);
                usTodoModel.setSendFlag(sendFalg);
                usTodoModel.setTypeStatus(ToDoEnum.open.getValue() + "-" + ToDoEnum.close.getValue());
                usTodoModel = usTodoModelService.savaLocalTodoData(usTodoModel);
            }
        }
        log.warn("usTodoModelService>>>close>>>>>【{}】",JacksonUtils.obj2json(usTodoModel));
        return usTodoModel;
    }

    /**
     * 删除统一代办
     *
     * @param actBusinessStatus 业务状态操作对象
     * @param userName          审批人
     */
    public void cancelTodo(ActBusinessStatus actBusinessStatus, String userName) {}

    @SuppressWarnings("unused")
    private UsTodoModel saveTodoModel(ActBusinessStatus businessStatus,String todoUser,String oaHtmlUrl,String urlParams){
        UsTodoModel usTodoModel = new UsTodoModel();
        try {
            usTodoModel.setWorkItemId(businessStatus.getWorkItemId().toString());
            usTodoModel.setActivityDefId(businessStatus.getActivityDefId());
            usTodoModel.setProcessInstanceId(businessStatus.getProcessInstId().toString());
            usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
            usTodoModel.setBusinessStatusId(businessStatus.getId());
            usTodoModel.setProcessDefId(businessStatus.getProcessDefId().toString());
            usTodoModel.setCreator(businessStatus.getCreateUserCode());
            usTodoModel.setCreatedTime(businessStatus.getCreateTime());
            usTodoModel.setModifiedTime(businessStatus.getEndTime());
            usTodoModel.setModifier(todoUser);
            usTodoModel.setUserName(todoUser);
            usTodoModel.setSender(todoUser);
            usTodoModel.setTitle(businessStatus.getReceiptTitle());
            usTodoModel.setTypeStatus(ToDoEnum.open.getValue());
            usTodoModel.setOaHtmlUrl(oaHtmlUrl);
            usTodoModel.setUrlParams(urlParams);
            usTodoModel.setWorkFlag(true);
            usTodoModel.setEnabled(true);
            usTodoModel.setSendFlag(false);
            usTodoModel.setSendDate(businessStatus.getEndTime());
            //先保存本地推送记录，然后再调用接口平台推送代办
            log.debug("usTodoModelService>>>insert>>>>>开始" + usTodoModel.toString());
            UsTodoModel usTodoModelTmp = new UsTodoModel();
            usTodoModelTmp.setBusinessKey(businessStatus.getBusinessKey());
            usTodoModelTmp.setWorkItemId(StrUtil.toString(businessStatus.getWorkItemId()));
            Specification<UsTodoModel> usTodoModelSpecification = usTodoModelService.getSpecification(usTodoModelTmp);
            usTodoModelTmp = usTodoModelService.findOne(usTodoModelSpecification);
            if (ObjectUtil.isEmpty(usTodoModelTmp)) {
                usTodoModel = usTodoModelService.savaLocalTodoData(usTodoModel);
            }
            log.debug("usTodoModelService>>>insert>>>>>" + JacksonUtils.obj2json(usTodoModel));
            log.debug("usTodoModelService>>>insert>>>>>结束");
        }catch (Exception e){
            Exceptions.printException( e );
        }
        return usTodoModel;
    }

    //根据账号查询账号信息
    private Map<String, HqUserInfo> findByUsernames(Set<String> usernameSet) {
        Map<String, HqUserInfo> userMap = Maps.newHashMap();
        Assert.notEmpty(usernameSet , "人员信息不能为空！");
        String usernames = usernameSet.stream().collect(Collectors.joining(","));
        Map<String, String> userAccountMap = Maps.newHashMap();
        if (!StrUtil.equals(profilesActive , "prd")){
            List<String> userAllList = Lists.newArrayList();
            usernameSet.stream().forEach(username -> userAllList.add(username + "@ha.cmcc"));
            Specification<UsernameAccount> build = Specifications.<UsernameAccount>and()
                    .in("username", userAllList)
                    .build();
            Iterable<UsernameAccount> usernameAccounts = usernameAccountService.findAllNoPage(build);
            for (UsernameAccount usernameAccount : usernameAccounts) {
                userAccountMap.put(usernameAccount.getUsername().replace("@ha.cmcc", ""), usernameAccount.getAccount().replace("@ha.cmcc", ""));
            }
        }
        JsonResponse jsonResponse = uumsSysUserinfoApi.findUserByUsernameNoPageNoSession(Constants.APP_CODE, Constants.USER_ADMIN, usernames);
        if (jsonResponse.getErrcode() == 0) {
            List<Map<String, Object>> data = (List<Map<String, Object>>) jsonResponse.getData();
            if (CollectionUtil.isNotEmpty(data)) {
                Map<String, Object> map = data.get(0);
                List<Map<String, Object>> userList = MapUtil.get(map, "user", new TypeReference<List<Map<String, Object>>>() {});
                if (CollectionUtil.isNotEmpty(userList)) {
                    userList.stream().forEach(user -> {
                        if (StrUtil.equals(MapUtil.getStr(user, "treeType"), "user")) {
                            String id = MapUtil.getStr(user, "id");
                            HqUserInfo userInfo = HqUserInfo.builder().userId(id).userName(MapUtil.getStr(user, "name")).idType("0").build();
                            if (!StrUtil.equals(profilesActive , "prd")) {
                                userInfo.setUserId(MapUtil.getStr(userAccountMap , id));
                                userInfo.setUserName( "xx" + userInfo.getUserName().substring(userInfo.getUserName().length() -1 , userInfo.getUserName().length()));
                            }
                            userMap.put( id , userInfo);
                        }
                    });
                }
            }
        }
        log.warn("查询人员数据信息：{}" , JacksonUtils.obj2json(userMap));
        return userMap;
    }

    @SuppressWarnings("unused")
    private UsTodoModel saveTodoModelToRead(ActBusinessStatus businessStatus, String todoUser, String oaHtmlUrl, String urlParams, Boolean sendFalg, String sysId, String sysName, String staus, String wfId) {
        UsTodoModel usTodoModel = new UsTodoModel();
        try {
            usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
            usTodoModel.setWorkItemId(wfId);
            Specification<UsTodoModel> usTodoModelSpecification = usTodoModelService.getSpecification(usTodoModel);
            usTodoModel = usTodoModelService.findOne(usTodoModelSpecification);
        } catch (Exception e) {
            Exceptions.printException(e);
        } finally {
            if (cn.hutool.core.util.ObjectUtil.isEmpty(usTodoModel)) {
                usTodoModel = new UsTodoModel();
                usTodoModel.setWorkItemId(wfId);
                usTodoModel.setActivityDefId(businessStatus.getActivityDefId());
                usTodoModel.setProcessInstanceId(StrUtil.toString(businessStatus.getProcessInstId()));
                usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
                usTodoModel.setBusinessStatusId(businessStatus.getId());
                usTodoModel.setProcessDefId(StrUtil.toString(businessStatus.getProcessDefId()));
                usTodoModel.setCreator(businessStatus.getCreateUserCode());
                usTodoModel.setCreatedTime(businessStatus.getCreateTime());
                usTodoModel.setModifiedTime(businessStatus.getEndTime());
                usTodoModel.setModifier(todoUser);
                usTodoModel.setUserName(todoUser);
                usTodoModel.setSender(todoUser);
                usTodoModel.setTitle(businessStatus.getReceiptTitle());
                usTodoModel.setTypeStatus(ToDoEnum.open.getValue());
                usTodoModel.setOaHtmlUrl(oaHtmlUrl);
                usTodoModel.setUrlParams(urlParams);
                usTodoModel.setWorkFlag(true);
                usTodoModel.setEnabled(true);
                usTodoModel.setSendFlag(sendFalg);
                usTodoModel.setSendDate(businessStatus.getEndTime());
                usTodoModel.setSysId(sysId);
                usTodoModel.setSysName(sysName);
                usTodoModel = usTodoModelService.savaLocalTodoData(usTodoModel);
            }
        }
        log.warn("usTodoModelService>>>saveTodoModelToRead>>>>>【{}】", JacksonUtils.obj2json(usTodoModel));
        return usTodoModel;
    }

    private UsTodoModel closeTodoModelDoRead(ActBusinessStatus businessStatus, String todoUser, Boolean sendFalg, String status, String wfId) {
        UsTodoModel usTodoModel = new UsTodoModel();
        try {
            usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
            usTodoModel.setSender(todoUser);
            usTodoModel.setWorkFlag(true);
            usTodoModel.setWorkItemId(wfId);
            Specification<UsTodoModel> usTodoModelSpecification = usTodoModelService.getSpecification(usTodoModel);
            usTodoModel = usTodoModelService.findOne(usTodoModelSpecification);
            log.warn("TodoBusOperatorService>>>>>>>usTodoModel>>>>>获取参数" + JacksonUtils.obj2json(usTodoModel));
        } catch (Exception e) {
            Exceptions.printException(e);
        } finally {
            if (ObjectUtil.isNotEmpty(usTodoModel)) {
                usTodoModel.setWorkFlag(false);
                usTodoModel.setSendFlag(sendFalg);
                usTodoModel.setTypeStatus(ToDoEnum.open.getValue() + "-" + ToDoEnum.close.getValue());
                usTodoModel = usTodoModelService.savaLocalTodoData(usTodoModel);
            }
        }
        log.warn("usTodoModelService>>>closeTodoModelDoRead>>>>>【{}】", JacksonUtils.obj2json(usTodoModel));
        return usTodoModel;
    }

}
