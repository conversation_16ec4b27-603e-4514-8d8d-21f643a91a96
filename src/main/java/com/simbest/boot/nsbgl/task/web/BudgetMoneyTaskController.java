package com.simbest.boot.nsbgl.task.web;

import com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OutputParameters;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.nsbgl.budget.client.BudgetMoneyClient;
import com.simbest.boot.nsbgl.budget.service.IBudgetMoneyService;
import com.simbest.boot.nsbgl.task.BudgetMoneyTask;
import com.simbest.boot.nsbgl.task.SendMailTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * @Auther: ztz
 * @Date: 2020/4/24 16:21
 * @Description:
 */
@Api(description = "预算管理-预算同步定时任务接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/task/budgetMoney")
public class BudgetMoneyTaskController {
    @Autowired
    private BudgetMoneyTask budgetMoneyTask;

    @Autowired
    private IBudgetMoneyService budgetMoneyService;

    @Autowired
    private SendMailTask sendMailTask;
    /**
     *创建待办
     * @return
     */
    @ApiOperation(value = "创建待办",notes = "创建待办")
    @RequestMapping(value = {"/syncBudgetMoney","/api/syncBudgetMoney"},method = {RequestMethod.GET,RequestMethod.POST})
    public JsonResponse syncBudgetMoney(){
        return JsonResponse.success( budgetMoneyTask.execute() );
    }



    /**
     *创建待办
     * @return
     */
    @ApiOperation(value = "手动同步待办",notes = "手动同步待办")
    @RequestMapping(value = {"/manualsyncBudgetMoney","/api/manualsyncBudgetMoney"},method = {RequestMethod.GET,RequestMethod.POST})
    public JsonResponse manualsyncBudgetMoney(@RequestParam String preBudgetNum){
        budgetMoneyService.manualtaskSynchronizationBudget(preBudgetNum);
        return JsonResponse.defaultSuccessResponse();
    }


    /**
     *创建待办
     * @return
     */
    @ApiOperation(value = "发送邮件",notes = "发送邮件")
    @RequestMapping(value = {"/sendMail","/api/sendMail"},method = {RequestMethod.GET,RequestMethod.POST})
    public JsonResponse sendMail(){
        return JsonResponse.success( sendMailTask.execute() );
    }

    /**
     * 测试资产查询接口
     * @param currentPage 当前页，默认1
     * @param pageSize 页大小，默认10
     * @param totalRecord 总记录数，默认0
     * @param provinceCode 省份代码，默认使用预算查询中的省份代码
     * @return
     */
    @ApiOperation(value = "测试资产查询接口", notes = "测试资产查询接口")
    @RequestMapping(value = {"/testSearchAsset", "/api/testSearchAsset"}, method = {RequestMethod.GET, RequestMethod.POST})
    public JsonResponse testSearchAsset(@RequestParam(required = false, defaultValue = "1") String currentPage,
                                       @RequestParam(required = false, defaultValue = "10") String pageSize,
                                       @RequestParam(required = false, defaultValue = "0") String totalRecord,
                                       @RequestParam(required = false, defaultValue = "4772276805467173986") String provinceCode) {
        try {
            log.info("开始测试资产查询接口，参数：currentPage={}, pageSize={}, totalRecord={}, provinceCode={}",
                    currentPage, pageSize, totalRecord, provinceCode);

            OutputParameters response = BudgetMoneyClient.searchAsset(
                    new BigDecimal(currentPage),
                    new BigDecimal(pageSize),
                    new BigDecimal(totalRecord),
                    provinceCode
            );

            log.info("资产查询接口调用成功");
            return JsonResponse.success(response);
        } catch (Exception e) {
            log.error("资产查询接口调用失败", e);
            return JsonResponse.fail("资产查询失败: " + e.getMessage());
        }
    }

}
