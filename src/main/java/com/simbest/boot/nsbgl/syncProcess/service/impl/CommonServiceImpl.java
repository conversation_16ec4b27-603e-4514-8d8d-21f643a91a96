package com.simbest.boot.nsbgl.syncProcess.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.bps.exceptions.BpsWorkFlowBusinessRuntimeException;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.process.bussiness.service.impl.ActBusinessStatusService;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.nsbgl.apply.model.ApplicationAndEquipment;
import com.simbest.boot.nsbgl.apply.model.ApplicationMaintainForm;
import com.simbest.boot.nsbgl.apply.repository.ApplicationMaintainFormRepository;
import com.simbest.boot.nsbgl.apply.repository.WfWorkItemRepository;
import com.simbest.boot.nsbgl.apply.service.IApplicationAndEquipmentService;
import com.simbest.boot.nsbgl.apply.service.IApplicationMaintainFormService;
import com.simbest.boot.nsbgl.approvalmsg.service.IApprovalMsgConfigService;
import com.simbest.boot.nsbgl.mainbills.model.UsPmInstence;
import com.simbest.boot.nsbgl.syncProcess.model.SysProcessErrorLog;
import com.simbest.boot.nsbgl.syncProcess.service.ICommonServer;
import com.simbest.boot.nsbgl.syncProcess.service.ISysProcessErrorLogService;
import com.simbest.boot.nsbgl.syncProcess.util.AsyncLoginUtil;
import com.simbest.boot.nsbgl.util.Constants;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.wf.process.service.IProcessInstanceService;
import com.simbest.boot.wf.process.service.IWorkItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.xpath.operations.Bool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.simbest.boot.config.MultiThreadConfiguration.MULTI_THREAD_BEAN;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2021/3/22</strong><br>
 * <strong>Modify on : 2021/3/22</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Service
@SuppressWarnings("All")
@Slf4j
public class CommonServiceImpl implements ICommonServer {

    private static final String PROCESS_TYPE_START = "startType";
    private static final String PROCESS_TYPE_APPROVAL = "approvalType";

    @Autowired
    private AsyncLoginUtil asyncLoginUtil;

    @Autowired
    private IProcessInstanceService processInstanceService;

    @Autowired
    private IWorkItemService workItemService;

    @Autowired
    private ISysProcessErrorLogService processErrorLogService;

//    @Autowired
//    private IApplicationMaintainFormService applicationMaintainFormService;

    @Autowired
    private ApplicationMaintainFormRepository applicationMaintainFormRepository;


    @Autowired
    private IApplicationAndEquipmentService applicationAndEquipmentService;


    @Autowired
    private WfWorkItemRepository wfWorkItemRepository;

    @Autowired
    private  ActBusinessStatusService actBusinessStatusService;

    @Autowired
    private IApprovalMsgConfigService approvalMsgConfigService;

    /**
     * 启动流程并流转下一步
     *
     * @param currentUserCode 当前登陆人
     * @param nextUserName    下一步提交人
     * @param outcome         决策项
     * @param message         意见
     * @param pmInstence      主单据数据
     */
    //@Async(MULTI_THREAD_BEAN)
    @Override
    public void startProcessAndApproval(String currentUserCode, String nextUserName, String processName, String outcome, String message, UsPmInstence pmInstence) {
        Long workItemId = null;
        long ret = 1;
        String flag = "false";//用于表示设备信息是否保存完善
        try {
            //bps登陆
            asyncLoginUtil.AsyncLoginUtil(currentUserCode);
            //封装启动流程的参数
            Map<String, Object> variables = new HashMap<String, Object>();
            variables.put("inputUserId", currentUserCode);
            variables.put("receiptId", pmInstence.getId());
            variables.put("title", pmInstence.getPmInsTitle());
            variables.put("code", pmInstence.getPmInsId());
            variables.put("currentUserCode", currentUserCode);
            variables.put("activityDefID", Constants.FLOW_LOCATION_START);
            variables.put("appCode", Constants.APP_CODE);
            //启动流程
            workItemId = processInstanceService.startProcessAndSetRelativeData(processName, pmInstence.getPmInsTitle(), pmInstence.getPmInsTitle(), false, variables);
            //workItemService.updateWorkItemStatusById(String.valueOf(workItemId), 100);
            //流转下一步
            if (workItemId != 0) {
//                wfWorkItemRepository.updateWprkModel(String.valueOf(workItemId));
//                WfWorkItemModel newWork = wfWorkItemRepository.findNewWork(String.valueOf(workItemId));
//                wfWorkItemRepository.saveAndFlush(newWork);
                //提交审批
                //设备和表单信息保存完善后进行工单流程，否则不进行流转
                if (pmInstence.getPmInsType().indexOf("D") != -1 || pmInstence.getPmInsType().indexOf("F") != -1 || pmInstence.getPmInsType().indexOf("E") != -1) {
                    ApplicationMaintainForm applicationMaintainForm = applicationMaintainFormRepository.findInfoByPmInsId(pmInstence.getPmInsId());
                    if (ObjectUtil.isNotEmpty(applicationMaintainForm)) {
                        if (StringUtils.isNotEmpty(applicationMaintainForm.getId())) {
                            List<ApplicationAndEquipment> equipmentList = applicationAndEquipmentService.getEquipmentList(applicationMaintainForm.getId());
                            if (equipmentList.size() > 0) {
                                ret = processApprovalInterface(workItemId, currentUserCode, nextUserName, outcome, message, pmInstence);
                                if (ret < 1) {
                                    //错误处理
                                    String errorMsg = "流转失败";
                                    processErrorLogService.saveErrorLog(currentUserCode, nextUserName, processName, workItemId, outcome, message, PROCESS_TYPE_APPROVAL, errorMsg, pmInstence);
                                }
                            } else {
                                //流程启动失败，保存失败信息
                                String errorMsg = "启动流程失败";
                                processErrorLogService.saveErrorLog(currentUserCode, nextUserName, processName, workItemId, outcome, message, PROCESS_TYPE_START, errorMsg, pmInstence);
                            }
                        }

                    }

                } else {
                    ret = processApprovalInterface(workItemId, currentUserCode, nextUserName, outcome, message, pmInstence);
                    if (ret == 0) {
                        //错误处理
                        String errorMsg = "流转失败";
                        processErrorLogService.saveErrorLog(currentUserCode, nextUserName, processName, workItemId, outcome, message, PROCESS_TYPE_APPROVAL, errorMsg, pmInstence);
                    }
//                    else {
//                        //流程启动失败，保存失败信息
//                        String errorMsg = "启动流程失败";
//                        processErrorLogService.saveErrorLog(currentUserCode, nextUserName, processName, workItemId, outcome, message, PROCESS_TYPE_START, errorMsg, pmInstence);
//                    }
                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            //错误处理
            String errorMsg = e.getMessage().length() < 210 ? e.getMessage() : e.getMessage().substring(0, 210);
            //判断异常的断点，如果在启动流程之前则是启动失败，在启动流程之后则是流转失败
            if (workItemId != null && workItemId > 0) {
                processErrorLogService.saveErrorLog(currentUserCode, nextUserName, processName, workItemId, outcome, message, PROCESS_TYPE_APPROVAL, errorMsg, pmInstence);
            } else {
                processErrorLogService.saveErrorLog(currentUserCode, nextUserName, processName, workItemId, outcome, message, PROCESS_TYPE_APPROVAL, errorMsg, pmInstence);
            }
        }
    }

    /**
     * 流转下一步
     *
     * @param workItemId      当前环节id
     * @param currentUserCode 当前登录人
     * @param nextUserName    下一步提交人
     * @param outcome         决策项
     * @param message         意见
     * @param pmInstence
     */
    @Async(MULTI_THREAD_BEAN)
    @Override
    public void processApproval(long workItemId, String currentUserCode, String nextUserName, String outcome, String message, UsPmInstence pmInstence,String location,String userName) {
        //bps登陆
        asyncLoginUtil.AsyncLoginUtil(currentUserCode);
        //更新当前环节的状态信息
        workItemService.updateWorkItemStatusById(String.valueOf(workItemId), 100);
         long ret = processApprovalInterface(workItemId, currentUserCode, nextUserName, outcome, message, pmInstence);
        ActBusinessStatus actBusinessStatus= null;
        String processInstId="";
        try {
            actBusinessStatus = actBusinessStatusService.queryActBusinessStatusByPmInstId(pmInstence.getPmInsId());
            if(actBusinessStatus!=null){
                processInstId=actBusinessStatus.getProcessInstId().toString();
            }
        } catch (BpsWorkFlowBusinessRuntimeException e) {
            e.printStackTrace();
        }
        if(ret>0){
            approvalMsgConfigService.sendMsg(pmInstence.getPmInsId(), processInstId, location, outcome, pmInstence.getPmInsTitle(), Constants.APP_CODE, Constants.APP_NAME, userName,nextUserName);

        }
        if (ret == 0) {
            //错误处理
            String errorMsg = "流转失败";
            processErrorLogService.saveErrorLog(currentUserCode, nextUserName, null, workItemId, outcome, message, PROCESS_TYPE_APPROVAL, errorMsg, pmInstence);
        }
    }

    /**
     * 处理异常的流程
     *
     * @return
     */
    @Override
    public Map<String, Object> dealProcess() {
        Map<String, Object> map = Maps.newHashMap();
        int sucessCount = 0;
        int failedCount = 0;
        int allCount = 0;
        try {
            //获取所有未处理过的异常
            Specification<SysProcessErrorLog> build = Specifications.<SysProcessErrorLog>and()
                    .eq("status", "0")
                    .build();
            Iterable<SysProcessErrorLog> logs = processErrorLogService.findAllNoPage(build);
            //循环处理异常
            for (SysProcessErrorLog log : logs) {
                allCount++;
                UsPmInstence usPmInstence = new UsPmInstence();
                usPmInstence.setId(log.getBusinessKey());
                usPmInstence.setPmInsId(log.getPmInsId());
                usPmInstence.setPmInsTitle(log.getTitle());
                //启动流程
                if (StrUtil.equals(PROCESS_TYPE_START, log.getType())) {
                    log = startProcessAndApprovalNoAsync(log);
                }
                //流转
                else {
                    log = processApprovalNoAsync(log);
                }
                processErrorLogService.update(log);
                //记录成功|失败的数量
                if (StrUtil.equals("1", log.getStatus())) {
                    sucessCount++;
                } else {
                    failedCount++;
                }
            }
            map.put("allCount", allCount);
            map.put("sucessCount", sucessCount);
            map.put("failedCount", failedCount);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return map;
    }

    /**
     * 不使用异步处理未启动成功的数据
     *
     * @param log
     * @return
     */
    private SysProcessErrorLog startProcessAndApprovalNoAsync(SysProcessErrorLog log) {
        Long workItemId = null;
        try {
            //bps登陆
            asyncLoginUtil.AsyncLoginUtil(log.getCurrentUserCode());
            //封装启动流程的参数
            Map<String, Object> variables = new HashMap<String, Object>();
            variables.put("inputUserId", log.getCurrentUserCode());
            variables.put("receiptId", log.getBusinessKey());
            variables.put("title", log.getTitle());
            variables.put("code", log.getPmInsId());
            variables.put("currentUserCode", log.getCurrentUserCode());
            variables.put("activityDefID", Constants.FLOW_LOCATION_START);
            variables.put("appCode", Constants.APP_CODE);
            //启动流程
            workItemId = processInstanceService.startProcessAndSetRelativeData(log.getProcessName(), log.getTitle(), log.getTitle(), false, variables);
            //流转下一步
            if (workItemId != 0) {

                //提交审批
                UsPmInstence usPmInstence = new UsPmInstence();
                usPmInstence.setId(log.getBusinessKey());
                usPmInstence.setPmInsId(log.getPmInsId());
                usPmInstence.setPmInsTitle(log.getTitle());
                long ret = processApprovalInterface(workItemId, log.getCurrentUserCode(), log.getNextUserName(), log.getOutcome(), log.getMessage(), usPmInstence);
                log.setStatus("1");
                if (ret == 0) {
                    log.setStatus("-1");
                }
            } else {
                //流程启动失败，保存失败信息
                log.setStatus("-1");
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            log.setStatus("-1");
        }
        return log;
    }

    /**
     * 不使用异步的方式流转未成功流转的数据
     *
     * @param log
     * @return
     */
    private SysProcessErrorLog processApprovalNoAsync(SysProcessErrorLog log) {
        //bps登陆
        asyncLoginUtil.AsyncLoginUtil(log.getCurrentUserCode());
        UsPmInstence usPmInstence = new UsPmInstence();
        usPmInstence.setId(log.getBusinessKey());
        usPmInstence.setPmInsId(log.getPmInsId());
        usPmInstence.setPmInsTitle(log.getTitle());
        long ret = processApprovalInterface(Long.parseLong(log.getWorkItemId()), log.getCurrentUserCode(), log.getNextUserName(), log.getOutcome(), log.getMessage(), usPmInstence);
        log.setStatus("1");
        if (ret == 0) {
            log.setStatus("-1");
        }
        return log;
    }


    /**
     * 流转下一步接口
     *
     * @param workItemId      当前工作项id
     * @param currentUserCode 当前登陆人
     * @param nextUserName    下一步提交人
     * @param outcome         连线规则
     * @param message         信息
     * @param pmInstence
     * @return
     *///
    private long processApprovalInterface(Long workItemId, String currentUserCode, String nextUserName, String outcome, String message, UsPmInstence pmInstence) {
        System.out.println("**********************************************22222222222222222222222222222");
        long ret = 0;
        //封装流转下一步接口参数
        Map<String, Object> map = new HashMap<>();
        if (nextUserName != null) {
            map.put("inputUserId", nextUserName);//指定下一审批人
        }
        map.put("outcome", outcome);
        map.put("receiptId", String.valueOf(pmInstence.getId()));
        map.put("title", pmInstence.getPmInsTitle());
        map.put("code", pmInstence.getPmInsId());
        map.put("currentUserCode", currentUserCode);
        map.put("appCode", Constants.APP_CODE);
        try {

            //添加流程审批意见
            int status = workItemService.submitApprovalMsg(workItemId, message);
            if (status == 1) {

                //根据工作项ID完成工作项 如果第三个参数为true，则启用事务分割；如果第二个参数为false，则不启用事务分割
                ret = workItemService.finishWorkItemWithRelativeData(workItemId, map, false);
            } else {
                ret = 0;
            }
        } catch (Exception e) {
            ret = 0;
            Exceptions.printException(e);
        }
        return ret;
    }
}
