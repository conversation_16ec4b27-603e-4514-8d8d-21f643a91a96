package com.simbest.boot.nsbgl.apply.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.mzlion.easyokhttp.HttpClient;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.process.bussiness.service.IActBusinessStatusService;
import com.simbest.boot.bps.process.listener.model.WFNotificationInstModel;
import com.simbest.boot.bps.process.listener.model.WFProcessInstModel;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.bps.process.listener.service.IWfProcessInstModelService;
import com.simbest.boot.cmcc.nmsg.MsgPostOperatorService;
import com.simbest.boot.cmcc.nmsg.model.Content;
import com.simbest.boot.cmcc.nmsg.model.ShrotMsg;
import com.simbest.boot.nsbgl.apply.model.ApplicationAndEquipment;
import com.simbest.boot.nsbgl.apply.model.ApplicationMaintainForm;
import com.simbest.boot.nsbgl.apply.repository.ApplicationAndEquipmentRepository;
import com.simbest.boot.nsbgl.apply.repository.ApplicationMaintainFormRepository;
import com.simbest.boot.nsbgl.apply.repository.WfWorkItemRepository;
import com.simbest.boot.nsbgl.apply.service.IApplicationAndEquipmentService;
import com.simbest.boot.nsbgl.apply.service.IApplicationMaintainFormService;
import com.simbest.boot.nsbgl.budget.model.ReceiptBill;
import com.simbest.boot.nsbgl.budget.service.IBudgetMoneyService;
import com.simbest.boot.nsbgl.budget.service.IReceiptBillService;
import com.simbest.boot.nsbgl.equipment.model.EquipmentRepairRecord;
import com.simbest.boot.nsbgl.equipment.service.IEquipmentRepairRecordService;
import com.simbest.boot.nsbgl.external.model.ApplicationMaintainFormForApi;
import com.simbest.boot.nsbgl.mainbills.model.UsPmInstence;
import com.simbest.boot.nsbgl.mainbills.repository.UsPmInstenceRepository;
import com.simbest.boot.nsbgl.mainbills.service.IUsPmInstenceService;
import com.simbest.boot.nsbgl.syncProcess.service.ICommonServer;
import com.simbest.boot.nsbgl.util.*;
import com.simbest.boot.security.IAuthService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.security.SimplePosition;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.templates.MessageEnum;
import com.simbest.boot.util.CustomBeanUtil;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.MapUtil;
import com.simbest.boot.util.PaginationHelp;
import com.simbest.boot.util.distribution.id.IdGenerator;
import com.simbest.boot.util.distribution.id.RedisIdGenerator;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import com.simbest.boot.wf.process.service.IProcessInstanceService;
import com.simbest.boot.wf.process.service.IWFNotificationService;
import com.simbest.boot.wf.process.service.IWfOptMsgService;
import com.simbest.boot.wf.process.service.IWorkItemService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.IteratorUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @description: 设备信息变更业务接口
 * @author: jingwenhao
 * @date: 2019/3/8 16:02
 * @version: 1.0
 */
@Slf4j
@Transactional
@Service
@SuppressWarnings("ALL")
public class ApplicationMaintainFormServiceImpl extends LogicService<ApplicationMaintainForm, String> implements IApplicationMaintainFormService {


    private ApplicationMaintainFormRepository maintainFormRepository;

    @Autowired
    public ApplicationMaintainFormServiceImpl(ApplicationMaintainFormRepository repository) {
        super(repository);
        this.maintainFormRepository = repository;
    }

    @Autowired
    private UumsSysAppApi uumsSysAppApi;
    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private IUsPmInstenceService usPmInstenceService;

    @Autowired
    private IProcessInstanceService processInstanceService;

    @Autowired
    private IWorkItemService workItemService;

    @Autowired
    private IActBusinessStatusService statusService;

    @Autowired
    private WfWorkItemRepository wfWorkItemRepository;

    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private IWFNotificationService iwfNotificationService; // 抄送业务接口

    @Autowired
    private IEquipmentRepairRecordService repairRecordService;// 设备维修记录接口

    @Autowired
    private IBudgetMoneyService budgetMoneyService; // 预算管理-预算金额业务接口

    @Autowired
    private ISysFileService sysFileService;// 文件业务接口

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi; // uums 组织接口

    @Autowired
    private IWfProcessInstModelService wfProcessInstModelService; // 流程实例业务接口

    @Autowired
    private RedisIdGenerator idGenerator;

    @Autowired
    private IWorkItemService workItemManager;

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private IReceiptBillService receiptBillService;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Autowired
    private PaginationHelp paginationHelp;

    @Autowired
    private IWfOptMsgService wfOptMsgService;

    @Autowired
    private IApplicationAndEquipmentService applicationAndEquipmentService;
    @Autowired
    private MsgPostOperatorService msgPostOperatorService;
    @Autowired
    private UsPmInstenceRepository usPmInstenceRepository;

    @Autowired
    private ApplicationAndEquipmentRepository applicationAndEquipmentRepository;

    @Autowired
    private BpsConfig bpsConfig;


    @Autowired
    private ICommonServer commonServer;
    String param1 = "/action/applicationMaintainForm";

    /**
     * 提交审批流程
     *
     * @param source          来源
     * @param currentUserCode 当前人
     * @param workItemId      活动实例id
     * @param outcome         决策连线规则z
     * @param location        当前环节
     * @param bodyParam
     * @param formId          表单id
     * @return
     */
    @Override
    public JsonResponse startSubmitProcess(String source, String currentUserCode, String workItemId, String outcome, String location, String copyLocation, Map<String, Object> bodyParam, String formId, String notificationId) {
        if (StringUtil.isNotEmpty(workItemId)) {
            //更新当前环节的状态信息
            workItemService.updateWorkItemStatusById(String.valueOf(workItemId), 100);
        }
        JsonResponse jsonResponse = new JsonResponse();
        /**处理bodyParam传来的参数**/
        if (bodyParam != null && bodyParam.size() > 0) {
            Map<String, Object> map = (Map<String, Object>) bodyParam.get("flowParam");
            if (map != null && map.size() > 0) {
                /**获取表单数据**/
                ApplicationMaintainForm form = null;
                Map<String, Object> formData1 = (Map<String, Object>) map.get("formData");
                /**手机端操作时，若没有传表单数据必须传表单id**/
                if (formData1 != null && !formData1.isEmpty()) {
                    form = JacksonUtils.json2obj(JacksonUtils.obj2json(formData1), ApplicationMaintainForm.class);
                } else {
                    if (!StringUtils.isEmpty(formId) && Constants.MOBILE.equals(source)) {
                        form = this.findById(formId);
                    }
                }
                /**获取下一步审批人和抄送人**/
                List<Map<String, String>> tempList = (List<Map<String, String>>) map.get("nextUserName");
                Map<String, String> temp = new HashMap<>();
                String nextUserName = "";
                if (null != tempList && !tempList.isEmpty()) {
                    temp = tempList.get(0);
                    nextUserName = temp.get("value");
                }
                tempList = (List<Map<String, String>>) map.get("copyNextUserNames");
                String copyNextUserNames = "";
                if (null != tempList && !tempList.isEmpty()) {
                    for (Map<String, String> mapObj : tempList) {
                        String copyName = mapObj.get("value");
                        if (!StringUtils.isEmpty(copyName)) {
                            copyNextUserNames = copyName + "," + copyNextUserNames;
                        }
                    }
                }
                /**获取下一步审批意见和抄送意见**/
                String message = map.get("message") != null ? map.get("message").toString() : null;
                String copyMessage = map.get("copyMessage") != null ? map.get("copyMessage").toString() : null;
                //如果表单id不为null，则走审批流程
                if (null != form && form.getId() != null && (!StringUtils.isEmpty(workItemId) || !StringUtils.isEmpty(notificationId))) {

                    jsonResponse = saveSubmitTask(form, workItemId, outcome, message, nextUserName, location, copyLocation, copyMessage, copyNextUserNames, source, currentUserCode, notificationId);
                } else {
                    //发起流程
                    jsonResponse = startProcess(form, nextUserName, outcome, message, source, currentUserCode);//创建提交
                }
            }
        }
        System.out.println("*****************************************************************11111111111111111111111");
        return jsonResponse;
    }

    /**
     * 查询工单详情
     *
     * @param id 工单id
     * @return
     */
    @Override
    public JsonResponse findByOne(String id) {
        // 返回数据
        Map<String, Object> returnMap = Maps.newHashMap();
        // 查询工单
        ApplicationMaintainForm form = this.findById(id);

        if (form != null) {
            //获取设备信息
            List<ApplicationAndEquipment> applicationAndEquipments = applicationAndEquipmentService.getEquipmentList(form.getId());
            form.setEquipmentList(applicationAndEquipments);
            // 处理相关附件
            form = this.updateFormFiles(form);
            // 查询流程实例id
            String pmInsId = form.getPmInsId();
            WFProcessInstModel wfProcessInstModel = null;
            Specification<WFProcessInstModel> specification = Specifications.<WFProcessInstModel>and()
                    .eq((!StringUtils.isEmpty(pmInsId)), "receiptCode", pmInsId) //主单据ID
                    .eq("enabled", Constants.COMMON_STATUS_ENABLED)
                    .build();
            Iterable<WFProcessInstModel> allNoPage = wfProcessInstModelService.findAllNoPage(specification);
            List<WFProcessInstModel> list = IteratorUtils.toList(allNoPage.iterator());
            if (list.size() > 0) {
                wfProcessInstModel = list.get(0);
                returnMap.put("processInstId", wfProcessInstModel.getProcessInstId());
            }
            returnMap.put("maintain", form);
        }
        return JsonResponse.success(returnMap);
    }

    /**
     * 外部接口：查询时间段工单信息
     *
     * @return
     */
    @Override
    public JsonResponse queryOrder(int page, int size, Map<String, Object> paramsMap) {
        // 29bc5873257bdace93001cbc8a7a8dd2
        JsonResponse jsonResponse = JsonResponse.defaultSuccessResponse();
        // 开始时间
        LocalDateTime startDateTime = null;
        // 结束时间
        LocalDateTime endDateTime = null;
        // 返回的map集合
        Map<String, Object> returnMap = Maps.newHashMap();
        // 返回的list集合
        List<ApplicationMaintainFormForApi> returnList = null;
        SysOperateLog operateLog = new SysOperateLog();
        try {
            // 模拟登陆
            loginUtils.adminLogin();
            String startDateStr = "";// 开始时间
            String endDateStr = ""; // 结束时间
            String uniqueNum = ""; // 工单编号
            if (!StringUtils.isEmpty(paramsMap.get("startDate"))) {
                startDateStr = (String) paramsMap.get("startDate");
            }
            if (!StringUtils.isEmpty(paramsMap.get("endDate"))) {
                endDateStr = (String) paramsMap.get("endDate");
            }
            if (!StringUtils.isEmpty(paramsMap.get("uniqueNum"))) {
                uniqueNum = (String) paramsMap.get("uniqueNum");
            }
            String param2 = "/queryOrder";
            String params = ",startDate=" + startDateStr + ",startDate=" + endDateStr + ",uniqueNum=" + uniqueNum;
            operateLog.setInterfaceParam(params);
            operateLog.setOperateFlag("API");
            operateLog.setOperateInterface(param1 + param2);
            // 开始日期
            if (!StringUtils.isEmpty(startDateStr)) {
                //startDateStr = DateUtil.getCurrentStr();
                // 开始日期
                LocalDate startDate = LocalDate.parse(startDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                // 00：00：00
                LocalTime startTime = LocalTime.of(0, 0, 0);
                startDateTime = LocalDateTime.of(startDate, startTime);
            }
            // 结束日期
            if (!StringUtils.isEmpty(endDateStr)) {
                //endDateStr = DateUtil.getCurrentStr();
                // 结束日期
                LocalDate endDate = LocalDate.parse(endDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                // 23：59：59
                LocalTime endTime = LocalTime.of(23, 59, 59);
                endDateTime = LocalDateTime.of(endDate, endTime);
            }
            // TODO 优化sql
            // 动态参数
            Map<String, Object> map = Maps.newHashMap();

            /**定义sql**/
//            StringBuffer sql1 = new StringBuffer("select t.created_time  as \"createdTime\", t.APPLY_PHONE   as \"applyPhone\"," +
//                    " t.id ,t.PM_INS_ID as \"pmInsId\", t.APPLY_USER as \"applyUser\", " +
//                    " t.APPLY_UNIT_CODE as \"applyUnitCode\",t.APPLY_UNIT_NAME as \"applyUnitName\", t.UNIQUE_NUM as \"uniqueNum\", " +
//                    " t.TITLE as \"title\",t.ORDER_TYPE_VALUE as \"orderTypeValue\", t.ORDER_TYPE_NAME as \"orderTypeName\", " +
//                    " t.EQUIPMENT_ID as \"equipmentId\",t.EQUIPMENT_NAME as \"equipmentName\", t.EQUIPMENT_NUM as \"equipmentNum\", " +
//                    " t.PRINCIPAL_NAME as \"principalName\",t.BRAND_NAME as \"brandName\", t.REPAIR_INSTRUCTIONS as \"repairInstructions\", " +
//                    " t.RECEIVE_INSTRUCTIONS as \"receiveInstructions\",t.BUDGET_NUM as \"budgetNum\", t.BUDGET_NAME as \"budgetName\", " +
//                    " t.TOTAL_AMOUNT as \"totalAmount\",t.USABLE_AMOUNT as \"usableAmount\", t.ESTIMATED_COST as \"estimatedCost\", " +
//                    " t.ESTIMATED_EXPLANATION as \"estimatedExplanation\",t.CONSUMABLE_PRICE as \"consumablePrice\", t.STAFF_COSTS as \"staffCosts\", " +
//                    " t.actual_amount as \"actualAmount" +
//                    " FROM US_APPLICATION_MAINTAIN_FORM t, ACT_BUSINESS_STATUS a" +
//                    "  where t.PM_INS_ID = a.RECEIPT_CODE and a.CURRENT_STATE='7' and t.ENABLED=1 and t.order_type_value='0' ");
//


            /**定义sql**/
            StringBuffer sql1 = new StringBuffer("select t.created_time," +
                    "       t.APPLY_PHONE," +
                    "       t.id," +
                    "       t.PM_INS_ID," +
                    "       t.APPLY_USER," +
                    "       t.APPLY_UNIT_CODE," +
                    "       t.APPLY_UNIT_NAME," +
                    "       t.UNIQUE_NUM," +
                    "       t.TITLE," +
                    "       t.ORDER_TYPE_VALUE," +
                    "       t.ORDER_TYPE_NAME," +
                    "       t.EQUIPMENT_ID," +
                    "       t.EQUIPMENT_NAME," +
                    "       t.EQUIPMENT_NUM," +
                    "       t.PRINCIPAL_NAME," +
                    "       t.BRAND_NAME," +
                    "       t.REPAIR_INSTRUCTIONS," +
                    "       t.RECEIVE_INSTRUCTIONS," +
                    "       t.BUDGET_NUM," +
                    "       t.BUDGET_NAME," +
                    "       t.TOTAL_AMOUNT," +
                    "       t.USABLE_AMOUNT," +
                    "       t.ESTIMATED_COST," +
                    "       t.ESTIMATED_EXPLANATION," +
                    "       t.CONSUMABLE_PRICE," +
                    "       t.STAFF_COSTS," +
                    "       t.actual_amount," +
                    "       t.busi_reg_area_code," +
                    "       t.busi_reg_area_name, a.update_time as last_Update_Time" +
                    "  FROM US_APPLICATION_MAINTAIN_FORM t, ACT_BUSINESS_STATUS a" +
                    " where t.PM_INS_ID = a.RECEIPT_CODE" +
                    "   and a.CURRENT_STATE = '7'" +
                    "   and t.ENABLED = 1" +
                    "   and t.order_type_value = '0'");


            /**根据条件拼接sql**/
            if (!StringUtils.isEmpty(startDateTime)) {
                sql1.append(" and a.end_time >:startDateTime ");
                map.put("startDateTime", DateUtil.xmlDate2Date(DateUtil.localDateTimeToXmlDate(startDateTime)));
            }
            if (!StringUtils.isEmpty(endDateTime)) {
                sql1.append(" and a.end_time <:endDateTime ");
                //Date.from( localDateTime.atZone( ZoneId.systemDefault()).toInstant())
                map.put("endDateTime", DateUtil.xmlDate2Date(DateUtil.localDateTimeToXmlDate(endDateTime)));
            }
            if (!StringUtils.isEmpty(uniqueNum)) {
                sql1.append(" and t.unique_num =:uniqueNum ");
                map.put("uniqueNum", uniqueNum);
            }
            List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql1.toString(), map);
            list = FormatTool.formatConversion(list);//驼峰转换
            Page<List<Map<String, Object>>> pages = paginationHelp.getPageList(list, page, size, "desc", "createdTime");
            List<List<Map<String, Object>>> listList = pages.getContent();
            if (listList != null && listList.size() > 0) {
                for (int i = 0; i < listList.size(); i++) {
                    Map<String, Object> dataMap = (Map<String, Object>) listList.get(i);
                    //获取表单id
//                    List<ApplicationAndEquipment> equipmentList = Lists.newArrayList();
                    List<Map<String, Object>> equipmentList2 = Lists.newArrayList();
                    Map<String, Object> mmp = CollUtil.newHashMap();
                    equipmentList2 = applicationAndEquipmentService.getEquipmentListByTZ((String) dataMap.get("id"));
                    for (Map<String, Object> map2 : equipmentList2) {
                        if (String.valueOf(map2.get("consumableSourceValue")) == null || String.valueOf(map2.get("staffCostsSourceValue")) == null) {
                            continue;
                        }
                   /*     if ("*******.6".contains(String.valueOf(map2.get("consumableSourceValue")))) {
//                            applicationAndEquipment.setConsumablePrice("0");
                            map2.put("setConsumablePrice","0");
                        }*/
                        if ("1,2,3,4,5".contains(String.valueOf(map2.get("staffCostsSourceValue")))) {
//                            applicationAndEquipment.setStaffCosts("0");
                            map2.put("staffCosts", "0");
                        }
                    }
                    //提示1：维修人工费来源1-5，维修配件来源1-4、6：该类型金额的填写仅做维修工单审批及费用统计分析，该部分金额报账请在报账系统直接报账，无需经过台账凭设备维修工单报账，传至台账系统时会自动变为0。
                    //提示2：维修人工费来源6，维修配件来源5、7：该类型金额的填写既做维修工单审批及费用统计分析，又需经过台账凭设备维修工单报账，传至台账系统报账时金额不变。
                   /* if (equipmentList2.size() > 0) {
                        for (ApplicationAndEquipment applicationAndEquipment : equipmentList) {
                            if (applicationAndEquipment.getConsumableSourceValue() == null || applicationAndEquipment.getStaffCostsSourceValue() == null) {
                                continue;
                            }
                            if ("*******.6".contains(applicationAndEquipment.getConsumableSourceValue())) {
                                applicationAndEquipment.setConsumablePrice("0");
                            }
                            if ("1,2,3,4,5".contains(applicationAndEquipment.getStaffCostsSourceValue())) {
                                applicationAndEquipment.setStaffCosts("0");
                            }
                        }
                    }*/
                    dataMap.put("equipmentList", equipmentList2);
                }
            }
            // TODO 从act_buiness查询已归档工单
            //List<String> receiptCodes = maintainFormRepository.selectReceiptCodes(Constants.FLOW_DEFNAME_SBGL_EQUIPMENT_MAINTAIN_BRANCH);
            //
            //Pageable pageable = this.getPageable(page, size, "desc", "createdTime");
            //Specification<ApplicationMaintainForm> specification = Specifications.<ApplicationMaintainForm>and()
            //        .gt(!StringUtils.isEmpty(startDateStr), "createdTime", startDateTime)//开始日期
            //        .lt(!StringUtils.isEmpty(endDateStr), "createdTime", endDateTime)//结束日期
            //        .eq(!StringUtils.isEmpty(uniqueNum), "uniqueNum", uniqueNum)//工单编号
            //        .eq("orderTypeValue", Constants.ORDER_TYPE_VALUE_EQUIPMENT)//工单类型，默认只查设备维修
            //
            //        .in(receiptCodes != null && !receiptCodes.isEmpty(), "pmInsId", receiptCodes)//主单据id
            //        .eq("enabled", Constants.COMMON_STATUS_ENABLED) //状态可用
            //        .build();
            //// Page<ApplicationMaintainForm> pages = this.findAll(specification, pageable);
            //List<ApplicationMaintainForm> list = this.findAllNoPage(specification);
            //if (list != null && !list.isEmpty()) {
            //    returnList = new ArrayList<>();
            //    for (ApplicationMaintainForm form : list) {
            //        ApplicationMaintainFormForApi formForApi = new ApplicationMaintainFormForApi();
            //        BeanUtils.copyProperties(form, formForApi);
            //        returnList.add(formForApi);
            //    }
            //    //转为分页list
            //    List<ApplicationMaintainForm> pagination = PageTool.pagination(returnList, page, size);
            //    Page<ApplicationMaintainForm> pages = new PageImpl<>(pagination, pageable, returnList.size());
            //    jsonResponse = JsonResponse.success(pages);
            //}
            jsonResponse = JsonResponse.success(pages);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            jsonResponse = JsonResponse.fail(e.toString());
        } finally {
            /**保存操作记录**/
            // operateLogService.saveLog(operateLog);
        }
        return jsonResponse;
    }

    /**
     * 获取报账平台回执数据
     *
     * @param uniqueNum
     * @return
     */
    @Override
    public ReceiptBill getAccount(String uniqueNum) {
        try {

            //uniqueNum   工单编号

            String claimNo = "";            //报账单编号
            String claimType = "";          //报账费用类别
            String budgetNum = "";          //报账_预算编号
            String budgetName = "";         //报账预算名称
            String invoiceType = "";        //发票类型
            String price = "";              //价款(元)（不含税）
            String taxAmount = "";          //税额(元)
            String total = "";              //价税合计(元)
            String totalActual = "";        //实际金额(维修费实际发生值(元) 维修费实际发生值为报账时实际占用预算的金额，等于加穗合计减去税额。)

            /**
             * 先拿工单编号在'us_receipt_bill'表中进行查询，看有没有相应的数据
             * -->如果有数据，传回去
             * -->如果没有数据，调用接口进行查询
             *      -->如果查询到数据，讲该条数据插入到'us_receipt_bill'表中，方便以后查询操作
             *      -->如果没有数据，返回null
             * */
            ReceiptBill bill = receiptBillService.findByUniqueNum(uniqueNum);
            if (bill != null) {
                return bill;
            } else {
                //如果数据库中没有数据，调用接口进行查询
                String url = bpsConfig.billUrl + "/account/rstf/applyAccount/repair";

                JsonObject jsonObject = HttpClient.post(url)
                        .queryString("pageNum", 1)
                        .queryString("pageSize", 10)
//                    .queryString("startTime", "")
//                    .queryString("endTime", "")
                        .queryString("uniqueNum", uniqueNum)
                        .asBean(JsonObject.class);
                // 获取到数据，根据实际情况，是否需要特殊处理
                JsonArray jsonArray = jsonObject.getAsJsonArray("rows");

                if (jsonArray != null && jsonArray.size() != 0) {
                    JsonObject object = jsonArray.get(0).getAsJsonObject();
                    claimNo = object.get("CLAIM_NO").toString().replace("\"", "");//报账单编号
                    String iClaimType = object.get("APPLY_TYPE").toString().replace("\"", "");//报账费用类型
                    String claimTypeValue = iClaimType;
                    //如果类型为 0-配件费、 1-人工费、 2-配件费+人工费
                    if (Constants.APPLY_TYPE_MATERIAL.equals(iClaimType)) {
                        claimType = Constants.MATERIAL_FEE;
                    } else if (Constants.APPLY_TYPE_HUMAN.equals(iClaimType)) {
                        claimType = Constants.HUMAN_FEE;
                    } else {
                        claimType = Constants.MATERIAL_AND_HUMAN_FEE;
                    }
                    budgetNum = object.get("BUDGET_PRJ_NUM").toString().replace("\"", "");//台账_预算编号
                    budgetName = object.get("BUDGET_PRJ_NAME").toString().replace("\"", "");//台账_预算名称
                    Double iPrice = object.get("PRICE_AMOUNT").getAsDouble();   //价款(元)（不含税）
                    price = iPrice.toString();
                    Double iTaxAmount = object.get("INPUT_TAX_AMOUNT").getAsDouble();   //税额(元)
                    taxAmount = iTaxAmount.toString();
                    //如果税额大于0就是专票
                    if (iTaxAmount > 0) {
                        invoiceType = Constants.INVOICE_TYPE_SPECIAL;   //专票
                    } else {
                        invoiceType = Constants.INVOICE_TYPE_NORMAL;    //普票
                    }
                    total = Double.toString(iPrice + iTaxAmount);//价税合计(元)
                    totalActual = price;

                    //获取ApplicationMaintainForm中的预算编号(BudgetNum)-->为了工单查询中预算表可以和台账回传信息表关联查
//                String billBudgetNum = maintainFormRepository.selectBillBudgetNumByUniqueNum(uniqueNum);
                    ApplicationMaintainForm one = maintainFormRepository.findOneActive(
                            Specifications.<ApplicationMaintainForm>and()
                                    .eq(!StringUtils.isEmpty(uniqueNum), "uniqueNum", uniqueNum)
                                    .eq("enabled", Constants.COMMON_STATUS_ENABLED) //状态可用
                                    .build()
                    );
                    //todo 调用台账接口返回数据,更新设备维修信息里面台账回执预算总额

                    //    -->如果调接口可以查到数据，把该条数据插入到'us_receipt_bill'表中，方便以后查询操作
                    ReceiptBill receiptBill = new ReceiptBill();
                    receiptBill.setBillBudgetNum(one == null ? null : one.getBudgetNum());
                    receiptBill.setUniqueNum(uniqueNum);
                    receiptBill.setClaimTypeValue(claimTypeValue);
                    receiptBill.setClaimType(claimType);
                    receiptBill.setBudgetName(budgetName);
                    receiptBill.setBudgetNum(budgetNum);
                    receiptBill.setClaimNo(claimNo);
                    receiptBill.setInvoiceType(invoiceType);
                    receiptBill.setPrice(price);
                    receiptBill.setTaxAmount(taxAmount);
                    receiptBill.setTotal(total);
                    receiptBill.setTotalActual(totalActual);
                    //向数据库中插入数据
                    log.debug("----获取台账插入台账信息start:" + receiptBill);
                    receiptBillService.insert(receiptBill);
                    log.debug("----获取台账插入台账信息end:" + receiptBill);
                    List<ApplicationAndEquipment> equipmentList = applicationAndEquipmentService.getEquipmentList(one.getId());
                    //开始对台账回执的金额进行百分比分配
                    BigDecimal receiptTotalActual = new BigDecimal(receiptBill.getTotalActual());//台账返回实际金额
                    BigDecimal actualAmount = new BigDecimal(one.getActualAmount());//工单实际维修金额
                    for (ApplicationAndEquipment applicationAndEquipment : equipmentList) {
                        //获取该设备的实际维修金额
                        BigDecimal equipmentActualAmount = new BigDecimal(applicationAndEquipment.getActualAmount());
                        BigDecimal equipmentTotalActual = receiptTotalActual.multiply(equipmentActualAmount.divide(actualAmount, 2, BigDecimal.ROUND_HALF_UP));
                        applicationAndEquipment.setTotalEquipmentActual(equipmentTotalActual.stripTrailingZeros().toPlainString());
                    }
                    if (equipmentList.size() > 0) {
                        one.setEquipmentList(equipmentList);
                        boolean flag = applicationAndEquipmentService.updateEquipment(one);//将数据保存到数据库中,后续查询就不会在通过这里
                        if (!flag) {
                            log.warn("设备台账金额回执保存失败!工单id为------" + one.getId() + "----getAccount");
                        }
                    }
                    return receiptBill;
                } else {
                    return null;
                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return null;
        }
    }

    /**
     * 提交起草流程
     *
     * @param applicationMaintainForm 表单
     * @param nextUserName            审批人
     * @param outcome                 连线规则
     * @param message                 审批意见
     * @param source                  来源
     * @param userCode                当前用户
     * @return
     */
    @Override
    public JsonResponse startProcess(ApplicationMaintainForm applicationMaintainForm, String nextUserName, String outcome, String message, String source, String userCode) {
        log.debug("起草接口----------startProcess---------->" + applicationMaintainForm.toString());
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = ",source=" + source + ",userCode=" + userCode + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName;
        operateLog.setInterfaceParam(params);
        long ret = 0;
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }

            /**校验表单和下一步审批人是否为空**/
            if (!StringUtils.isEmpty(nextUserName)) {
                IUser iUser = SecurityUtils.getCurrentUser();
                /**定义流程ID和流程类型**/
                String processDefId = "";
                String processType = "";
                Map<String, Object> processMap = this.getProcessMap(applicationMaintainForm);
                if (!processMap.isEmpty()) {
                    processDefId = (String) processMap.get("processName");
                    processType = (String) processMap.get("processType");
                }
                boolean flag = false;
                //流程ID和流程类型不为null，发起流程
                UsPmInstence usPmInstence = new UsPmInstence();
                if (!StringUtils.isEmpty(processDefId) && !StringUtils.isEmpty(processType)) {
                    if (StringUtils.isEmpty(applicationMaintainForm.getId())) {
                        usPmInstence.setPmInsType(processType);
                        /**保存业务数据**/
                        flag = this.saveApplicationMaintainForm(applicationMaintainForm, usPmInstence);
                    } else {
                        usPmInstence = usPmInstenceService.findByPmInsId(applicationMaintainForm.getPmInsId());
                        //如果有id,则是从草稿起草,需要更新
                        this.update(applicationMaintainForm);
                        //保存设备列表
                        applicationAndEquipmentService.saveEquipmentList(applicationMaintainForm);
                        if (applicationMaintainForm.getApplyFiles() != null && !applicationMaintainForm.getApplyFiles().isEmpty()) {
                            List<SysFile> applyFiles = applicationMaintainForm.getApplyFiles();
                            this.updateSysFiles(applyFiles, applicationMaintainForm.getPmInsId(), Constants.SYSFILE_TYPE_APPLY);
                        }
                        flag = true;
                    }
                    /**启动发起流程**/
                    if (flag) {
                        commonServer.startProcessAndApproval(iUser.getUsername(), nextUserName, processDefId, outcome, message, usPmInstence);
                    } else {
                        operateLog.setErrorMsg("保存表单数据失败");
                        JsonResponse.fail(null, "保存表单数据失败");
                    }
                } else {
                    operateLog.setErrorMsg("获取流程失败");
                    JsonResponse.fail(null, "操作失败，获取流程失败!");
                }
            } else {
                operateLog.setErrorMsg("表单为空或审批人为空");
                JsonResponse.fail(null, "操作失败，请确认申请表单和审批人!");
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            /**保存操作记录**/
            operateLogService.saveLog(operateLog);
        }
        String showMessage = this.getTemplate(nextUserName);
        //return ret > 0 ? JsonResponse.success(ret, showMessage) : JsonResponse.fail(ret, Constants.MESSAGE_FAIL);
        System.out.println("***************************************33333333333333333333333");
        return JsonResponse.success(1, showMessage);
    }

    /**
     * 审批提交
     *
     * @param applicationMaintainForm 表单
     * @param workItemId              活动实例id
     * @param outcome                 连线规则
     * @param message                 审批意见
     * @param nextUserName            审批人
     * @param location                当前环节
     * @param source                  来源
     * @param userCode                当前用户OA账号
     * @return
     */
    @Override
    public JsonResponse saveSubmitTask(ApplicationMaintainForm applicationMaintainForm, String workItemId, String outcome, String message, String nextUserName, String location, String copyLocation, String copyMessage, String copyNextUserNames, String source, String userCode, String notificationId) {
        log.debug("审批接口----------saveSubmitTask---------->" + applicationMaintainForm.toString());
        long ret = 0;
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = "ApplicationMaintainForm=" + applicationMaintainForm.getId() + ",workItemId=" + workItemId + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName + ",location=" + location + ",copyLocation=" + copyLocation + ",copyMessage"
                + copyMessage + ",copyNextUserNames=" + copyNextUserNames + ",source=" + source + ",userCode=" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            String formId = applicationMaintainForm.getId();
            if (!StringUtils.isEmpty(formId)) {
                String pmInsId = applicationMaintainForm.getPmInsId();
                operateLog.setBussinessKey(pmInsId);
                UsPmInstence pmInstence = usPmInstenceService.findByPmInsId(pmInsId);//获取主单据
                /**判断是否是从手机端还是PC端记录操作日志**/
                JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
                if (returnObj != null) {
                    return returnObj;
                }
                // 获取用户
                IUser user = SecurityUtils.getCurrentUser();
                /**相关流转审批操作**/
                if (pmInstence != null) {
                    /**审批流转**/
                    if (!StringUtils.isEmpty(workItemId)) {
                        if ("end".equals(outcome) || !StringUtils.isEmpty(nextUserName)) {
                            //所有流转给周文博(zhouwenbo),zhoupeng(周鹏)都会给hadmin1(发送一条短信)
                            if (!StringUtils.isEmpty(nextUserName) && nextUserName.equals(Constants.YZ_ADMIN)) {
                                this.sendMsg("hadmin1");
                            }
                            //ret = this.processApproval(Long.parseLong(workItemId), user.getUsername(), user.getTruename(), nextUserName, outcome, location, message, pmInstence);
                            commonServer.processApproval(Long.parseLong(workItemId), user.getUsername(), nextUserName, outcome, message, pmInstence, location, user.getUsername());
                            ret = 100;
                        } else {
                            operateLog.setErrorMsg("审批人不能为空");
                            return JsonResponse.fail("审批人不能为空");
                        }
                        /**审批环节发起抄送**/
                        //if (ret > 0 && !StringUtils.isEmpty(copyNextUserNames)) {//审批环节发起抄送
                        if (!StringUtils.isEmpty(copyNextUserNames)) {//审批环节发起抄送
                            this.saveNotification(user, workItemId, pmInstence, copyNextUserNames, copyMessage, copyLocation);
                        }
                    } else {//抄送通知
                        WFNotificationInstModel model = (WFNotificationInstModel) iwfNotificationService.getWFNotificationById(notificationId);
                        if (copyLocation != null && !StringUtils.isEmpty(copyNextUserNames)) {//继续通知
                            this.saveNotification(user, workItemId, pmInstence, copyNextUserNames, copyMessage, copyLocation);
                            if (model != null) {
                                ret = iwfNotificationService.updateNotificationStatus(model.getId(), Constants.COMMON_STATUS_1, null);
                            }
                        } else {//结束通知
                            if (model != null) {
                                ret = iwfNotificationService.updateNotificationStatus(model.getId(), Constants.COMMON_STATUS_1, copyMessage);
                            }
                        }
                    }

                    String orderTypeValue = applicationMaintainForm.getOrderTypeValue();// 工单类型
                    /**更新表单**/
                    ApplicationMaintainForm maintainForm = this.findById(formId);
                    // 如果location为“nsbgl.start”，起草人修改（多为退回修改）
                    if (ret > 0 && !StringUtils.isEmpty(location) &&
                            (location.equals(Constants.FLOW_LOCATION_START)
                                    || location.equals(Constants.FLOW_LOCATION_APPLY_SPECIFIC_PERSONNEL)// 发起部门具体人员办理
                                    || location.equals(Constants.FLOW_LOCATION_MAINTENANCE_SPECIFIC_PERSONNEL))) {//// 维修主管部门具体人员办理
                        // 更新工单

                        maintainForm.setActPminsType(applicationMaintainForm.getActPminsType());
                        maintainForm.setTaskStatus(applicationMaintainForm.getTaskStatus());
                        maintainForm.setTaskStatusName(applicationMaintainForm.getTaskStatusName());
                        maintainForm.setApplyDate(applicationMaintainForm.getApplyDate()); // 期望完成日期
                        maintainForm.setEquipmentNumber(applicationMaintainForm.getEquipmentNumber());//维修设备数量
                        maintainForm.setRepairInstructions(applicationMaintainForm.getRepairInstructions()); //设备维修说明
                        maintainForm.setReceiveInstructions(applicationMaintainForm.getReceiveInstructions());//耗材领用说明
                        maintainForm.setOrderTypeValue(applicationMaintainForm.getOrderTypeValue());//工单类型
                        maintainForm.setOrderTypeName(applicationMaintainForm.getOrderTypeName());//工单类型名称
                        maintainForm.setEquipmentList(applicationMaintainForm.getEquipmentList());
                        List<SysFile> applyFiles = applicationMaintainForm.getApplyFiles();// 附件信息
                        applicationAndEquipmentService.saveEquipmentList(maintainForm); //多设备进行设备信息更新
                        maintainForm.setEquipmentId(applicationMaintainForm.getEquipmentId());// 关联设备Id
                        maintainForm.setEquipmentName(applicationMaintainForm.getEquipmentName());// 关联设备名称
                        maintainForm.setEquipmentNum(applicationMaintainForm.getEquipmentNum());// 关联设备编号
                        maintainForm.setPrincipalName(applicationMaintainForm.getPrincipalName());// 关联责任人
                        maintainForm.setBrandName(applicationMaintainForm.getBrandName()); // 关联设备品牌
                        maintainForm.setItemModel(applicationMaintainForm.getItemModel());//关联设备规格型号

                        // 更新设备维修记录
                        List<EquipmentRepairRecord> repairRecordList = repairRecordService.findAllByFormId(applicationMaintainForm.getId());
                        for (EquipmentRepairRecord repairRecord : repairRecordList) {
                            repairRecord.setEquipmentId(applicationMaintainForm.getEquipmentId());//关联设备id
                            repairRecord.setEquipmentName(applicationMaintainForm.getEquipmentName());//关联设备名称
                            repairRecord.setEquipmentNum(applicationMaintainForm.getEquipmentNum());//关联设备编号
                            // 设备维修说明
                            if (!StringUtils.isEmpty(applicationMaintainForm.getRepairInstructions())) {
                                repairRecord.setApplyContent(applicationMaintainForm.getRepairInstructions());
                            }
                            // 耗材领用说明
                            if (!StringUtils.isEmpty(applicationMaintainForm.getReceiveInstructions())) {
                                repairRecord.setApplyContent(applicationMaintainForm.getReceiveInstructions());
                            }
                            repairRecordService.update(repairRecord);
                        }
                        //todo 多设备进行设备维修记录更新
                        repairRecordService.updateAllByFormId(maintainForm);
                        this.update(maintainForm);
                        // 是否更新附件
                        if (applyFiles != null && !applyFiles.isEmpty()) {
                            // 更新附件
                            this.updateSysFiles(applicationMaintainForm.getApplyFiles(), pmInsId, Constants.SYSFILE_TYPE_APPLY);
                        }
                    }
                    // 工单类型为“耗材领用”
                    if (ret > 0 && orderTypeValue.equals(Constants.COMMON_STATUS_1)) {
                        //增加待办工单的办理状态，拼接到工单标题后面
/*                        if(!StringUtils.isEmpty(maintainForm.getTaskStatus())){
                            String pmInsTitle = maintainForm.getTitle();
                            pmInstence.setPmInsTitle(pmInsTitle+"-"+maintainForm.getTaskStatusName());
                            UsPmInstence update = usPmInstenceService.update(pmInstence);
                        }*/

                        // 如果outcome为“nsbgl.start_affirm_end”，已办理，请起草人归档，则更新实际金额
                        if (outcome.equals(Constants.FLOW_DECISIONID_START_AFFIRM_END)) {
                            // 更新设备维修记录
                            //todo 多设备更新耗材领用状态下的工单  repairRecordService.updateType0ByFormId(maintainForm);
                            // EquipmentRepairRecord repairRecord = repairRecordService.findByOne(EquipmentRepairRecord.builder().maintainFormId(formId).build());

                            Specification<EquipmentRepairRecord> specification = Specifications.<EquipmentRepairRecord>and()
                                    .eq(!StringUtils.isEmpty(formId), "maintainFormId", formId)
                                    .eq("enabled", true)
                                    .build();
                            List<EquipmentRepairRecord> allNoPage = repairRecordService.findAllNoPage(specification);
                            for (EquipmentRepairRecord repairRecord : allNoPage) {
                                repairRecord.setMaintainDate(LocalDate.now()); // 办理日期
                                repairRecord.setMaintainUser(user.getTruename()); // 办理人
                                repairRecord.setActualAmount("---"); // 费用合计
                                repairRecord.setStatus(Constants.COMMON_STATUS_1);// 工单状态
                                repairRecordService.update(repairRecord);
                            }
                        }
                    }
                    // 工单类型为“设备维修”
                    if (ret > 0 && orderTypeValue.equals(Constants.COMMON_STATUS_0)) {
                        // 如果location为“nsbgl.administrator”，部门管理员审批
                        if (!StringUtils.isEmpty(location) && location.equals(Constants.FLOW_LOCATION_ADMINISTRATOR) && !outcome.equals(Constants.FLOW_DECISIONID_ADMINISTRATOR_RETURN_APPLY)) {
                            String budgetNum = applicationMaintainForm.getBudgetNum();// 预算编号
                            String estimatedCost = applicationMaintainForm.getEstimatedCost();// 维修费预估金额（元）
                            if (!StringUtils.isEmpty(budgetNum)) {
                                // 只有原始工单“预估金额”为空时，才会更新预算相关金额信息  TODO 取消本系统校验，与“预算平台”保持一致
                                //if (StringUtils.isEmpty(maintainForm.getEstimatedCost())) {
                                //    // 更新预算相关金额，增加预算占用金额
                                //    budgetMoneyService.updateBudgetMoney(budgetNum, estimatedCost);
                                //}
                                maintainForm.setBudgetNum(budgetNum);// 预算编号
                                maintainForm.setBudgetName(applicationMaintainForm.getBudgetName());// 预算名称
                                maintainForm.setUsableAmount(applicationMaintainForm.getUsableAmount());// 本系统预算可用金额(元)
                                maintainForm.setTotalAmount(applicationMaintainForm.getTotalAmount());//预算总额(元)

                                maintainForm.setEstimatedCost(estimatedCost); // 维修费预估金额（元）
                                maintainForm.setEstimatedExplanation(applicationMaintainForm.getEstimatedExplanation());//维修费预估说明
                            }
                            // 如果部门管理员录入实际发生费用相关信息
                            if (!StringUtils.isEmpty(applicationMaintainForm.getConsumablePrice())) {
                                maintainForm.setConsumablePrice(applicationMaintainForm.getConsumablePrice());//维修配件价格
                            }
                            if (!StringUtils.isEmpty(applicationMaintainForm.getStaffCosts())) {
                                maintainForm.setStaffCosts(applicationMaintainForm.getStaffCosts());//维修人工费用
                            }
                            if (!StringUtils.isEmpty(applicationMaintainForm.getActualAmount())) {
                                maintainForm.setActualAmount(applicationMaintainForm.getActualAmount());//维修费实际金额（元）
                            }
                            this.update(maintainForm);
                            // 更新附件
                            this.updateSysFiles(applicationMaintainForm.getActualFiles(), pmInsId, Constants.SYSFILE_TYPE_ACTUAL);
                            this.updateSysFiles(applicationMaintainForm.getEstimatedFiles(), pmInsId, Constants.SYSFILE_TYPE_ESTIMATED);
                            //todo 更新多设备信息
                            applicationAndEquipmentService.updateEquipment(applicationMaintainForm);

                        }
                        // 如果是“发起部门具体人员办理”或者“维修主管部门具体人员办理”,且不为请起草人归档
                        if ((location.equals(Constants.FLOW_LOCATION_APPLY_SPECIFIC_PERSONNEL) || location.equals(Constants.FLOW_LOCATION_MAINTENANCE_SPECIFIC_PERSONNEL))
                                && !outcome.equals(Constants.FLOW_DECISIONID_START_AFFIRM_END)) {
                            // 更新工单
                            applicationAndEquipmentService.updateEquipment(applicationMaintainForm);
                            if (!StringUtils.isEmpty(applicationMaintainForm.getConsumablePrice())) {
                                maintainForm.setConsumablePrice(applicationMaintainForm.getConsumablePrice());//维修配件价格
                            }
                            if (!StringUtils.isEmpty(applicationMaintainForm.getStaffCosts())) {
                                maintainForm.setStaffCosts(applicationMaintainForm.getStaffCosts());//维修人工费用
                            }
                            if (!StringUtils.isEmpty(applicationMaintainForm.getActualAmount())) {
                                maintainForm.setActualAmount(applicationMaintainForm.getActualAmount());//维修费实际金额（元）
                            }
                            this.update(maintainForm);
                        }
                        // 如果outcome为“nsbgl.start_affirm_end”，已办理，请起草人归档，则更新实际金额
                        if (outcome.equals(Constants.FLOW_DECISIONID_START_AFFIRM_END)) {
                            if (StringUtils.isEmpty(applicationMaintainForm.getConsumablePrice())) {
                                maintainForm.setConsumablePrice("0");//维修配件价格
                            } else {
                                maintainForm.setConsumablePrice(applicationMaintainForm.getConsumablePrice());//维修配件价格
                            }
                            if (StringUtils.isEmpty(applicationMaintainForm.getStaffCosts())) {
                                maintainForm.setStaffCosts("0");//维修人工费用
                            } else {
                                maintainForm.setStaffCosts(applicationMaintainForm.getStaffCosts());//维修人工费用
                            }
                            String actualAmount = applicationMaintainForm.getActualAmount();//维修费实际金额（元）
                            if (StringUtils.isEmpty(actualAmount)) {
                                actualAmount = "0";
                            }
                            maintainForm.setActualAmount(actualAmount);//维修费实际金额（元）
                            this.update(maintainForm);
                            // 更新附件
                            this.updateSysFiles(applicationMaintainForm.getActualFiles(), pmInsId, Constants.SYSFILE_TYPE_ACTUAL);
                            // 更新预算相关金额，释放预算占用金额，增加实际发生金额  TODO 取消本系统校验，与“预算平台”保持一致
                            //budgetMoneyService.updateBudgetMoney(maintainForm.getBudgetNum(), maintainForm.getEstimatedCost(), actualAmount);
                            applicationAndEquipmentService.updateEquipment(applicationMaintainForm);
                            // 更新设备维修记录 todo
                            repairRecordService.updateType0ByFormId(applicationMaintainForm);
                            List<EquipmentRepairRecord> repairRecordList = repairRecordService.findAllByFormId(formId);
                            for (EquipmentRepairRecord repairRecord : repairRecordList) {
                                repairRecord.setMaintainDate(LocalDate.now()); // 办理日期
                                repairRecord.setMaintainUser(user.getTruename()); // 办理人
                                repairRecord.setActualAmount(actualAmount); // 费用合计
                                repairRecord.setStatus(Constants.COMMON_STATUS_1);// 工单状态
                                repairRecordService.update(repairRecord);
                            }

                        }
                        // 如果outcome为退回起草人修改，则释放相关预算预占等金额
                        if (outcome.equals(Constants.FLOW_DECISIONID_ADMINISTRATOR_RETURN_APPLY)
                                || outcome.equals(Constants.FLOW_DECISIONID_MAINTENANCE_DEPARTMENT_LEADER_RETURN_APPLY)
                                || outcome.equals(Constants.FLOW_DECISIONID_DEPARTMENT_LEADER_RETURN_APPLY)) {
                            // 更新工单
                            /* TODO 取消本系统校验，与“预算平台”保持一致
                            // 获取预估金额，释放预占金额
                            String estimatedCost = maintainForm.getEstimatedCost();
                            // 获取预算编号
                            String budgetNum = maintainForm.getBudgetNum();
                            // 如果预估金额不为空，则释放预占金额
                            if (!StringUtils.isEmpty(estimatedCost)) {
                                budgetMoneyService.freeBudgetMoney(budgetNum, estimatedCost);
                            }
                            */

                            maintainForm.setBudgetNum(null);// 预算编号
                            maintainForm.setBudgetName(null);// 预算名称
                            maintainForm.setUsableAmount(null);// 本系统预算可用金额(元)
                            maintainForm.setTotalAmount(null);//预算总额(元)

                            maintainForm.setEstimatedCost(null); // 维修费预估金额（元）
                            maintainForm.setEstimatedExplanation(null);//维修费预估说明
                            this.update(maintainForm);
                            // 删除相关预估附件
                            this.deleteSysFiles(pmInsId, Constants.SYSFILE_TYPE_ESTIMATED);
                            //删除相关实际附件
                            this.deleteSysFiles(pmInsId, Constants.SYSFILE_TYPE_ACTUAL);
                        }
                    }
                } else {
                    operateLog.setErrorMsg("请联系管理员，主数据查找异常！pmInsId = " + pmInsId);
                    return JsonResponse.fail("请联系管理员，主数据查找异常！");
                }

            } else {
                operateLog.setErrorMsg("请联系管理员，表单对象为空！plan = " + applicationMaintainForm.toString());
                return JsonResponse.fail("请联系管理员，主数据查找异常！");
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        /**提醒流转下一步信息**/
        String showMessage = this.getTemplate(nextUserName);
        //return ret > 0 ? JsonResponse.success(ret, showMessage) : JsonResponse.fail(ret, Constants.MESSAGE_FAIL);
        return JsonResponse.success(1, showMessage);
    }

    private boolean sendMsg(String hadmin1) {
        String msg = Constants.APP_NAME + ":您收到一条设备维修待办,请及时办理!";
        Boolean isPostMsgOK = false;
        // 默认为管理员发送
        String sendUser = "hadmin";
        SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode(Constants.APP_CODE, sendUser);
        if (simpleApp != null) {
            isPostMsgOK = simpleApp.getIsSendMsg();
        }
        try {
            ShrotMsg shrotMsg = new ShrotMsg();
            Set<Content> contentSet = new HashSet<Content>();
            Content content = new Content();

            /**如果开关开启且待发列表不为空则发短信**/
            if (isPostMsgOK) {
                /**准备发送的参数**/
                shrotMsg.setAppCode(Constants.APP_CODE);
                content.setUsername(hadmin1);
                Map<String, String> paramMap = Maps.newHashMap();
                Set<String> resMsgPhones = Sets.newHashSet();
                // @param appName	            : 系统名称。
                // @param fromUser		        : 发送人。
                // @param itemSubject			: 事项主题。
                paramMap.put("appName", Constants.APP_CODE);
                paramMap.put("fromUser", content.getUsername());
                paramMap.put("itemSubject", msg);
                content.setMsgContent(msg);
                content.setImmediately(true);
                content.setSmsPriority(1);
                content.setPhoneNums(resMsgPhones);
                contentSet.add(content);
                shrotMsg.setContents(contentSet);
                isPostMsgOK = msgPostOperatorService.postMsg(shrotMsg);
            }

        } catch (Exception e) {
            log.error("TodoOpenServiceImpl execution Error>>>>>>>" + e.getMessage());
            Exceptions.printException(e);
            throw e;
        }
        return isPostMsgOK;
    }

    /**
     * 流转下一步
     *
     * @param workItemID      活动实例id
     * @param currentUserCode 当前登录人code
     * @param currentUserName 当前登录人姓名
     * @param nextUserName    审批人
     * @param outcome         连线规则
     * @param location        当前所处环节
     * @param message         审批意见
     * @param pmInstence      主单据
     * @return
     */
    private long processApproval(Long workItemID, String currentUserCode, String currentUserName, String
            nextUserName, String outcome, String location, String message, UsPmInstence pmInstence) {
        long ret;
        Map<String, Object> map = new HashMap<>();
        if (nextUserName != null) {
            map.put("inputUserId", nextUserName);//指定下一审批人
        }
        map.put("outcome", outcome);
        map.put("receiptId", pmInstence.getId());
        map.put("title", pmInstence.getPmInsTitle());
        map.put("code", pmInstence.getPmInsId());
        map.put("currentUserCode", currentUserCode);
        map.put("appCode", Constants.APP_CODE);
        try {
            //添加流程审批意见
            workItemService.submitApprovalMsg(workItemID, message);
            //根据工作项ID完成工作项 如果第三个参数为true，则启用事务分割；如果第二个参数为false，则不启用事务分割
            ret = workItemService.finishWorkItemWithRelativeData(workItemID, map, false);
        } catch (Exception e) {
            Exceptions.printException(e);
            ret = 0;
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return ret;
    }

    /**
     * 获取申请表单
     *
     * @param processInstId 流程实例id
     * @param workFlag      待办已办标识
     * @param source        来源
     * @param userCode      Oa账户
     * @param pmInsId
     * @return
     */
    @Override
    public JsonResponse getFormDetail(Long processInstId, String workFlag, String source, String userCode, String location, String pmInsId) {
        ApplicationMaintainForm form = null;
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/getFormDetail";
        String params = "processInstId=" + processInstId + ",workFlag=" + workFlag + ",source=" + source + ",userCode=" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            long l1 = System.currentTimeMillis();
            /**判断来源记录日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**点击办理查看详情**/
            if (null != processInstId) {
                ActBusinessStatus actBusinessStatus = statusService.getByProcessInst(processInstId);
                if (actBusinessStatus != null) {
                    log.debug("---------------进入actBusinessStatus:" + actBusinessStatus);
                    String formPmId = actBusinessStatus.getReceiptCode();
                    if ("E".equals(formPmId.substring(0, 1))) {
                        form = maintainFormRepository.getFromDetailByApplyPmInsId(formPmId);
                    } else {
                        String id = actBusinessStatus.getBusinessKey();
                        form = maintainFormRepository.getFromDetail(id);
                    }

                    // 判断部门管理员是否可以编辑预估金额信息
                    if (!StringUtils.isEmpty(location) && location.equals(Constants.FLOW_LOCATION_ADMINISTRATOR)) {
                        // 如果上一步审批人不是起草人，则部门管理员不可以编辑
                        Map<String, Object> mapParam = Maps.newHashMap();
                        mapParam.put("processInsId", processInstId);
                        mapParam.put("currentUser", SecurityUtils.getCurrentUserName());
                        // List<Map<String, Object>> wfWorkItemModels = workItemManager.queryWorkITtemDataMap(mapParam);
//                        List<Map<String, Object>> wfWorkItemModels = workItemService.queryWorkITtemDataMap(mapParam);
//                        if (!wfWorkItemModels.isEmpty()) {
//                            Map<String, Object> map = wfWorkItemModels.get(wfWorkItemModels.size() - 2);
//                            String activityDefId = (String) map.get("activityDefId");
//                            if (activityDefId.equals(Constants.FLOW_LOCATION_START) || activityDefId.equals(Constants.FLOW_LOCATION_ADMINISTRATOR)) {
//                                form.setIsStart(Constants.COMMON_STATUS_1);
//                            }
//                        }
                        List<WfWorkItemModel> workItemList = wfWorkItemRepository.findWfWorkItemModelByCode(pmInsId);
                        if (!workItemList.isEmpty()) {
                            WfWorkItemModel wfWorkItemModel = workItemList.get(workItemList.size() - 2);
                            String activityDefId = wfWorkItemModel.getActivityDefId();
                            if (activityDefId.equals(Constants.FLOW_LOCATION_START) || activityDefId.equals(Constants.FLOW_LOCATION_ADMINISTRATOR)) {
                                form.setIsStart(Constants.COMMON_STATUS_1);
                            }
                        }
                    }
                    if (form != null) {
                        log.debug("---------------更新附件前form" + form);
                        this.updateFormFiles(form);
                        log.debug("---------------更新附件后form" + form);

                        //工单类型  当为耗材领用时  不需要同步报账  没有报账回执信息
                        String orderTypeValue = form.getOrderTypeValue();
                        if (!StringUtils.isEmpty(location) && location.equals(Constants.FLOW_LOCATION_END) && orderTypeValue.equals(Constants.ORDER_TYPE_VALUE_EQUIPMENT)) {
                            log.debug("---------------获取台账start" + form);
                            String uniqueNum = form.getUniqueNum();
                            ReceiptBill bill = this.getAccount(uniqueNum);
                            form.setReceiptBill(bill);
                            log.debug("---------------获取台账end" + bill);
                        }
                        //todo 获取设备列表
                        List<ApplicationAndEquipment> equipmentList = applicationAndEquipmentService.getEquipmentList(form.getId());
                        log.debug("---------------获取设备列表" + equipmentList);
                        form.setEquipmentList(equipmentList);
                        operateLog.setBussinessKey(form.getPmInsId());
                    }
                }
                log.warn("查询待办数据耗时：{}" , System.currentTimeMillis() - l1);
            }
            if (null != pmInsId && !"E".equals(pmInsId.substring(0, 1))) {
                long l2 = System.currentTimeMillis();
                form = this.findByPmInsId(pmInsId);
                if (!StringUtils.isEmpty(location) && location.equals(Constants.FLOW_LOCATION_ADMINISTRATOR)) {
                    // 如果上一步审批人不是起草人，则部门管理员不可以编辑
                    Map<String, Object> mapParam = Maps.newHashMap();
                    mapParam.put("processInsId", processInstId);
                    mapParam.put("currentUser", SecurityUtils.getCurrentUserName());
                    //                   List<Map<String, Object>> wfWorkItemModels = workItemManager.queryWorkITtemDataMap(mapParam);
//                    if (!wfWorkItemModels.isEmpty()) {
//                        Map<String, Object> map = wfWorkItemModels.get(wfWorkItemModels.size() - 2);
//                        String activityDefId = (String) map.get("activityDefId");
//                        if (activityDefId.equals(Constants.FLOW_LOCATION_START) || activityDefId.equals(Constants.FLOW_LOCATION_ADMINISTRATOR)) {
//                            form.setIsStart(Constants.COMMON_STATUS_1);
//                        }
//                    }
                    List<WfWorkItemModel> workItemList = wfWorkItemRepository.findWfWorkItemModelByCode(pmInsId);
                    if (!workItemList.isEmpty()) {
                        WfWorkItemModel wfWorkItemModel = workItemList.get(workItemList.size() - 2);
                        String activityDefId = wfWorkItemModel.getActivityDefId();
                        if (activityDefId.equals(Constants.FLOW_LOCATION_START) || activityDefId.equals(Constants.FLOW_LOCATION_ADMINISTRATOR)) {
                            form.setIsStart(Constants.COMMON_STATUS_1);
                        }
                    }
                }
                if (form != null) {
                    log.debug("---------------更新附件前form" + form);
                    this.updateFormFiles(form);
                    log.debug("---------------更新附件后form" + form);

                    //工单类型  当为耗材领用时  不需要同步报账  没有报账回执信息
                    String orderTypeValue = form.getOrderTypeValue();
                    if (!StringUtils.isEmpty(location) && location.equals(Constants.FLOW_LOCATION_END) && orderTypeValue.equals(Constants.ORDER_TYPE_VALUE_EQUIPMENT)) {
                        log.debug("---------------获取台账start" + form);
                        String uniqueNum = form.getUniqueNum();
                        ReceiptBill bill = this.getAccount(uniqueNum);
                        form.setReceiptBill(bill);
                        log.debug("---------------获取台账end" + bill);
                    }
                    //todo 获取设备列表
                    List<ApplicationAndEquipment> equipmentList = applicationAndEquipmentService.getEquipmentList(form.getId());
                    log.debug("---------------获取设备列表" + equipmentList);

                    log.debug("---------------获取设备列表11111111111111111111" + equipmentList);

                    form.setEquipmentList(equipmentList);
                    log.debug("---------------获取设备列表222222222222222222222222" + equipmentList);
                    operateLog.setBussinessKey(form.getPmInsId());
                    log.debug("---------------获取设备列表33333333333333333333333" + equipmentList);
                    if ( StringUtil.isNotBlank(location)
                            && (location.equals(Constants.FLOW_LOCATION_MAINTENANCE_SPECIFIC_PERSONNEL)
                            || location.equals(Constants.FLOW_LOCATION_MAINTENANCE_DEPARTMENT_LEADER)
                            || location.equals(Constants.FLOW_LOCATION_MAINTENANCE_SPECIFIC_PERSONNEL))) {
                        form.setInMobile("1");
                    }
                }
                log.warn("第二部分耗时：{}" , System.currentTimeMillis() - l2);
            }
        } catch (Exception e) {
            log.debug("---------------获取设备列表4444444444444444444444444444" + e.toString());
            Exceptions.printException(e);
            operateLog.setErrorMsg(e.toString());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        } finally {
            //    operateLogService.saveLog(operateLog);
        }
        if (!StringUtils.isEmpty(location)) {
            //location.equals("nsbgl.administrator") ||
            if (location.equals("nsbgl.apply_specific_personnel")
                    || location.equals("nsbgl.business_specific_personnel")
                    || location.equals("nsbgl.maintenance_specific_personnel")
                    || location.equals("nsbgl.start")) {
                form.setInMobile(null);//不可以
            } else {
                if (location.equals("nsbgl.administrator") && "D".equals(form.getPmInsId().substring(0, 1))) {
                    form.setInMobile(null);//不可以
                } else {
                    form.setInMobile("1");
                }

            }
        }
        IUser user = SecurityUtils.getCurrentUser();
        log.debug("---------------获取设备列表555555555555555555555");
        form.setBelongCompanyTypeDictValue(user.getBelongCompanyTypeDictValue());
        log.debug("---------------获取设备列表66666666666666666666666");
        return JsonResponse.success(form);
    }

    /**
     * 发送通知产生待阅
     *
     * @param user
     * @param workItemId
     * @param usPmInstence
     * @param copyNextUserNames
     * @param copyMessage
     * @param copyLocation
     */
    private void saveNotification(IUser user, String workItemId, UsPmInstence usPmInstence, String copyNextUserNames, String copyMessage, String copyLocation) {
        String[] users = copyNextUserNames.split(",");
        WFNotificationInstModel model = new WFNotificationInstModel();
        WfWorkItemModel wfWorkItemModel = null;
        try {
            /**获取当前环节信息**/
            if (workItemId != null && !StringUtils.isEmpty(workItemId)) {
                wfWorkItemModel = (WfWorkItemModel) workItemService.getWorkItemByWorkItemId(Long.parseLong(workItemId));
                model.setProcessDefId(wfWorkItemModel.getProcessDefId());
                model.setProcessDefName(wfWorkItemModel.getProcessDefName());
                model.setProcessInstId(wfWorkItemModel.getProcessInstId());
                model.setActivityInstId(wfWorkItemModel.getActivityInstId());
                model.setActivityInstName(wfWorkItemModel.getActivityInstName());
                model.setWorkItemId(wfWorkItemModel.getWorkItemId());
                model.setWorkItemName(wfWorkItemModel.getWorkItemName());
            } else {
                model = (WFNotificationInstModel) iwfNotificationService.getNotificationByPmInsIdAndRecipient(usPmInstence.getPmInsId(), user.getUsername());
            }
            /**新增待阅信息**/
            if (users.length > 0) {
                for (String str : users) {
                    WFNotificationInstModel wfNotificationInstModel = new WFNotificationInstModel();
                    String recipient = str.split("-")[0];
                    String recipientName = str.split("-")[1];
                    wfNotificationInstModel.setSendUser(user.getUsername());
                    wfNotificationInstModel.setSendUserName(user.getTruename());
                    wfNotificationInstModel.setRecipient(recipient);
                    wfNotificationInstModel.setRecipientName(recipientName);
                    wfNotificationInstModel.setContent(copyMessage);
                    wfNotificationInstModel.setProcessDefId(model.getProcessDefId());
                    wfNotificationInstModel.setProcessDefName(model.getProcessDefName());
                    wfNotificationInstModel.setProcessInstId(model.getProcessInstId());
                    wfNotificationInstModel.setActivityInstId(model.getActivityInstId());
                    wfNotificationInstModel.setActivityInstName(model.getActivityInstName());
                    wfNotificationInstModel.setWorkItemId(model.getWorkItemId());
                    wfNotificationInstModel.setWorkItemName(model.getWorkItemName());
                    wfNotificationInstModel.setReceiptTitle(usPmInstence.getPmInsTitle());
                    wfNotificationInstModel.setReceiptCode(usPmInstence.getPmInsId());
                    wfNotificationInstModel.setStatus("0");
                    wfNotificationInstModel.setNextActivityDefId(copyLocation);
                    iwfNotificationService.saveLocalNotification(wfNotificationInstModel);
                }
            }
        } catch (Exception e) {
            log.debug(e.toString());
        }
    }

    /**
     * 保存业务数据
     *
     * @param applicationMaintainForm 表单
     * @param usPmInstence            主单据
     * @return
     * @throws Exception
     */
    boolean saveApplicationMaintainForm(ApplicationMaintainForm applicationMaintainForm, UsPmInstence usPmInstence) throws Exception {
        boolean flag = false;

        if (org.apache.commons.lang3.StringUtils.isNotEmpty(applicationMaintainForm.getId())) {
            flag = true;
        }

        IUser iuser = SecurityUtils.getCurrentUser();
        /**保存申请表单任务**/
        try {
            /**保存主单据**/
            String formId = applicationMaintainForm.getId();
            String numHead = "";
            // 获取工单编号
                /*if (applicationMaintainForm.getEquipmentNum().contains("WZ")) {
                    numHead = "M-WZ-";
                } else {
                    numHead = "M-DN-";
                }*/
            // 获取工单类型，用于生成uniqueNum编号 设备维修编号规则：M-WX-1907080001  耗材领用编号规则：M-HC-1907080001
            if (applicationMaintainForm.getOrderTypeValue().equals(Constants.ORDER_TYPE_VALUE_EQUIPMENT)) {
                numHead = "M-WX-";
            } else {
                numHead = "M-HC-";
            }
            String uniqueNum = numHead + idGenerator.getDateId("M", 4);  // M-1906110001
            //根据uniqueNum循环查询,一直到uniqueNum在数据库中不存在
            boolean uniqueFlag = true;
            int i = 0;//用于判断是否生成工单编号成功的次数
            while (uniqueFlag) {
                String budgetNum = maintainFormRepository.selectBillBudgetNumByUniqueNum(uniqueNum);
                if (StringUtils.isEmpty(budgetNum)) {
                    uniqueFlag = false;
                } else {
                    if (i >= 5) {
                        return false;
                    } else {
                        i++;
                    }
                }
            }
            if (StringUtils.isEmpty(formId)) {
                //获取主单据ID
                String pmInsId = NumRuleUtil.getPmInsId(usPmInstence.getPmInsType());
                usPmInstence.setPmInsId(pmInsId);
                usPmInstence.setPmInsTitle(applicationMaintainForm.getTitle());
                usPmInstence.setBelongCompanyCode(iuser.getBelongCompanyCode());
                usPmInstence.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                usPmInstence.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                usPmInstence.setBelongOrgCode(iuser.getBelongOrgCode());
                usPmInstence.setUniqueNum(uniqueNum);
                usPmInstenceService.insert(usPmInstence);
                usPmInstenceRepository.flush();
            }
            /**保存表单**/
            String usPmId = usPmInstence.getId();
            if (!StringUtils.isEmpty(usPmId)) {
                applicationMaintainForm.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                applicationMaintainForm.setBelongOrgCode(iuser.getBelongOrgCode());
                applicationMaintainForm.setBelongOrgName(iuser.getBelongOrgName());
                applicationMaintainForm.setBelongCompanyCode(iuser.getBelongCompanyCode());
                applicationMaintainForm.setBelongCompanyName(iuser.getBelongCompanyName());
                applicationMaintainForm.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                applicationMaintainForm.setBelongDepartmentName(iuser.getBelongDepartmentName());
                applicationMaintainForm.setPmInsId(usPmInstence.getPmInsId());
                applicationMaintainForm.setUniqueNum(uniqueNum);
                applicationMaintainForm.setParentCompanyCode(iuser.getBelongCompanyCodeParent());
                applicationMaintainForm.setParentCompanyName(iuser.getBelongCompanyNameParent());
                this.insert(applicationMaintainForm);
                formId = applicationMaintainForm.getId();
                /**保存设备列表**/
                applicationAndEquipmentService.saveEquipmentList(applicationMaintainForm);
            }
            if (!StringUtils.isEmpty(formId)) {
                /**处理申请附件**/
                if (applicationMaintainForm.getApplyFiles() != null && !applicationMaintainForm.getApplyFiles().isEmpty()) {
                    List<SysFile> applyFiles = applicationMaintainForm.getApplyFiles();
                    this.updateSysFiles(applyFiles, applicationMaintainForm.getPmInsId(), Constants.SYSFILE_TYPE_APPLY);
                }
                /**添加设备维修记录*/
                //支持多设备后需要进行list循环
//  todo              flag=this.addEquipmentRepairRecord(applicationMaintainForm);
                EquipmentRepairRecord repairRecord = new EquipmentRepairRecord();
                repairRecord.setMaintainFormId(formId);//关联维修工单Id
                repairRecord.setEquipmentId(applicationMaintainForm.getEquipmentId());//关联设备id
                repairRecord.setEquipmentName(applicationMaintainForm.getEquipmentName());//关联设备名称
                repairRecord.setApplyUser(applicationMaintainForm.getApplyUser());//报修人
                repairRecord.setEquipmentNum(applicationMaintainForm.getEquipmentNum());//关联设备编号
                repairRecord.setOrderTypeName(applicationMaintainForm.getOrderTypeName());//工单类型
                repairRecord.setApplyDate(LocalDate.now());
                // 设备维修说明
                if (!StringUtils.isEmpty(applicationMaintainForm.getRepairInstructions())) {
                    repairRecord.setApplyContent(applicationMaintainForm.getRepairInstructions());
                }
                // 耗材领用说明
                if (!StringUtils.isEmpty(applicationMaintainForm.getReceiveInstructions())) {
                    repairRecord.setApplyContent(applicationMaintainForm.getReceiveInstructions());
                }
                repairRecord.setStatus(Constants.COMMON_STATUS_0); // 默认状体为“申请中”
                repairRecordService.insert(repairRecord);
                if (!StringUtils.isEmpty(repairRecord.getId())) {
                    flag = true;
                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            //   TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            flag = false;
        }
        return flag;
    }

    /**
     * 添加设备维修记录
     *
     * @param applicationMaintainForm
     */
    private boolean addEquipmentRepairRecord(ApplicationMaintainForm applicationMaintainForm) {
        String formId = applicationMaintainForm.getId();
        List<ApplicationAndEquipment> equipmentList = applicationMaintainForm.getEquipmentList();
        if (equipmentList.size() > 0) {
            try {
                for (ApplicationAndEquipment applicationAndEquipment : equipmentList) {
                    EquipmentRepairRecord repairRecord = new EquipmentRepairRecord();
                    repairRecord.setMaintainFormId(formId);//关联维修工单Id
                    repairRecord.setEquipmentId(applicationAndEquipment.getEquipmentId());//关联设备id
                    repairRecord.setEquipmentName(applicationAndEquipment.getEquipmentName());//关联设备名称
                    repairRecord.setApplyUser(applicationMaintainForm.getApplyUser());//报修人
                    repairRecord.setEquipmentNum(applicationAndEquipment.getUniqueNum());//关联设备编号
                    repairRecord.setOrderTypeName(applicationMaintainForm.getOrderTypeName());//工单类型
                    repairRecord.setApplyDate(LocalDate.now());
                    // 设备维修说明
                    if (!StringUtils.isEmpty(applicationAndEquipment.getRepairInstructions())) {
                        repairRecord.setApplyContent(applicationAndEquipment.getRepairInstructions());
                    }
                    // 耗材领用说明
                    if (!StringUtils.isEmpty(applicationMaintainForm.getReceiveInstructions())) {
                        repairRecord.setApplyContent(applicationMaintainForm.getReceiveInstructions());
                    }
                    repairRecord.setStatus(Constants.COMMON_STATUS_0); // 默认状体为“申请中”
                    repairRecordService.insert(repairRecord);
                }
                return true;
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        }

        return false;
    }

    /**
     * 更新附件
     */
    private void updateSysFiles(List<SysFile> sysFileList, String pmInsId, String type) {
        if (sysFileList != null && !sysFileList.isEmpty()) {
            for (SysFile sysFile : sysFileList) {
                // 处理未关联工单附件
                if (StringUtils.isEmpty(sysFile.getPmInsId())) {
                    sysFile.setPmInsId(pmInsId);
                    sysFile.setPmInsType(type);
                    sysFileService.update(sysFile);
                }
            }
        }
    }

    /**
     * 删除工单相关附件
     *
     * @param pmInsId   工单id
     * @param pmInsType 文件类型
     */
    private void deleteSysFiles(String pmInsId, String pmInsType) {
        // 先查询此工单原有附件信息
        Specification<SysFile> specification = Specifications.<SysFile>and()
                .eq("pmInsId", pmInsId)
                .eq("pmInsType", pmInsType)
                .eq("enabled", Constants.COMMON_STATUS_ENABLED) //状态可用
                .build();
        Iterable<SysFile> itr = sysFileService.findAllNoPage(specification);
        // 如果有，则先删除相关附件
        if (itr.iterator().hasNext()) {
            sysFileService.deleteAll(itr);
        }
    }


    /**
     * 对表单附件处理
     *
     * @param form
     * @return
     */
    private ApplicationMaintainForm updateFormFiles(ApplicationMaintainForm form) {
        try {
            /**查询附件信息**/
            Specification<SysFile> specification = Specifications.<SysFile>and()
                    .eq("pmInsId", form.getPmInsId())
                    .eq("enabled", Constants.COMMON_STATUS_ENABLED) //状态可用
                    .build();
            Iterable<SysFile> itr = sysFileService.findAllNoPage(specification);
            List<SysFile> sysFileList = IteratorUtils.toList(itr.iterator());
            List<SysFile> applyFileList = new ArrayList<>();// 申请附件
            List<SysFile> estimatedFileList = new ArrayList<>();// 预估附件
            List<SysFile> actualFileList = new ArrayList<>();// 实际附件
            List<SysFile> applyerChecked = new ArrayList<>();//申请人手写签字
            /**处理表单相关附件**/
            if (!sysFileList.isEmpty()) {
                for (SysFile sysFile : sysFileList) {
                    // 附件类型为“申请附件”
                    if (sysFile.getPmInsType().equals(Constants.SYSFILE_TYPE_APPLY)) {
                        applyFileList.add(sysFile);
                    }
                    // 附件类型为“预估附件”
                    else if (sysFile.getPmInsType().equals(Constants.SYSFILE_TYPE_ESTIMATED)) {
                        estimatedFileList.add(sysFile);
                    } else if (sysFile.getPmInsType().equals("4")) {
                        applyerChecked.add(sysFile);
                    } else {
                        actualFileList.add(sysFile);
                    }
                }
            }
            if (!applyFileList.isEmpty()) {
                form.setApplyFiles(applyFileList);
            }
            if (!estimatedFileList.isEmpty()) {
                form.setEstimatedFiles(estimatedFileList);
            }
            if (!actualFileList.isEmpty()) {
                form.setActualFiles(actualFileList);
            }
            if (!applyerChecked.isEmpty()) {
                form.setApplyerChecked(applyerChecked);
            }
            return form;
        } catch (Exception e) {
            Exceptions.printException(e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return null;
        }
    }


    /**
     * 获取到流转到下一步提示信息
     *
     * @param nextUserName 审批人
     * @return
     */
    private String getTemplate(String nextUserName) {
        String showMessage = "";
        Map<String, String> paramMap = Maps.newHashMap();
        try {
            if (!StringUtils.isEmpty(nextUserName)) {
                IUser user = uumsSysUserinfoApi.findByKey(nextUserName, IAuthService.KeyType.username, Constants.APP_CODE); //审批人
                if (user != null) {
                    List<SimplePosition> simplePositionList = new ArrayList(user.getAuthPositions());
                    paramMap.put("companyName", user.getBelongCompanyName());
                    paramMap.put("departmentName", user.getBelongDepartmentName());
                    paramMap.put("trueName", user.getTruename());
                    paramMap.put("positionName", simplePositionList != null ? simplePositionList.get(0).getPositionName() : "");
                    showMessage = MessageEnum.MW000001.getMessage((Map) paramMap);
                }
            } else {
                showMessage = Constants.MESSAGE_SUCCESS;
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return showMessage;
    }

    /**
     * 获取流程
     *
     * @return
     */
    private Map<String, Object> getProcessMap(ApplicationMaintainForm applicationMaintainForm) {
        Map<String, Object> map = Maps.newHashMap();
        /**
         * 定义流程
         */
        if ("0".equals(applicationMaintainForm.getOrderTypeValue())) {
            //如果是设备维修用流程类型是D
            map.put("processName", Constants.FLOW_DEFNAME_SBGL_EQUIPMENT_MAINTAIN_BRANCH);
            map.put("processType", Constants.FLOW_TYPE_D);
        } else if ("1".equals(applicationMaintainForm.getOrderTypeValue())) {
            //如果是耗材领用流程类型是F
            map.put("processName", Constants.FLOW_DEFNAME_SBGL_EQUIPMENT_MAINTAIN_BRANCH);
            map.put("processType", Constants.FLOW_TYPE_F);
        }

        return map;
    }

    /**
     * 注销流程
     *
     * @param applicationMaintainForm
     * @return
     */
    @Override
    public int deleteProcess(Long processInstId, ApplicationMaintainForm applicationMaintainForm) {
        if (applicationMaintainForm.getId() == null) {
            return 1;
        } else {
            try {
                // 注销都是把相应表的enabled变为0
                // 牵涉到
                // Sys_file、US_APPLICATION_MAINTAIN_FORM、US_PM_INSTENCE、WF_OPTMSG_MODEL、
                // WF_PROCESS_INST_MODEL、WF_WORKITEM_MODEL
                // 这六个表，现在对sys_file这个表没有进行处理，其他进行逻辑删除处理。
                UsPmInstence pmInstence = usPmInstenceService.findByPmInsId(applicationMaintainForm.getPmInsId());
                if (processInstId != null) {
                    statusService.updateActBusDataByProInsId(processInstId);
                    //删除流程实例，审批意见，工作项
                    wfOptMsgService.deleteLocalDataByProInsId(processInstId);
                    workItemManager.deleteByProInsId(processInstId);
                    processInstanceService.deleteLocalDataByProInsId(processInstId);
                    //删除流程实例名称  BPS引擎操作
                    processInstanceService.deleteProcessInstance(processInstId);

                }
                if (pmInstence != null) {
                    usPmInstenceService.deleteByPmId(pmInstence.getId());
                }
                maintainFormRepository.deleteByFromId(applicationMaintainForm.getId(), LocalDateTime.now());
            } catch (Exception e) {
                Exceptions.printException(e);
                return 0;
            }
            log.debug("流程processInstId" + processInstId + "注销了流程。注销人为" + SecurityUtils.getCurrentUserName());
        }
        return 0;
    }

    @Override
    public ApplicationMaintainForm findByPmInsId(String pmInsId) {
        return maintainFormRepository.findByPmInsId(pmInsId);
    }

    /**
     * 保存/保存草稿
     *
     * @param form
     * @return
     */
    @Override
    public ApplicationMaintainForm saveForm(ApplicationMaintainForm form) throws Exception {
        if (StringUtils.isEmpty(form.getId())) {
            //首先生成主单据数据
            UsPmInstence usPmInstence = new UsPmInstence();
            if ("0".equals(form.getOrderTypeValue())) {
                usPmInstence.setPmInsType(Constants.FLOW_TYPE_D);
            } else if ("1".equals(form.getOrderTypeValue())) {
                usPmInstence.setPmInsType(Constants.FLOW_TYPE_F);
            }
            this.saveApplicationMaintainForm(form, usPmInstence);
        } else {
            this.update(form);
            /**保存设备列表**/
            applicationAndEquipmentService.saveEquipmentList(form);
            /**处理申请附件**/
            if (form.getApplyFiles() != null && !form.getApplyFiles().isEmpty()) {
                List<SysFile> applyFiles = form.getApplyFiles();
                this.updateSysFiles(applyFiles, form.getPmInsId(), Constants.SYSFILE_TYPE_APPLY);
            }
            // 更新附件
            this.updateSysFiles(form.getActualFiles(), form.getPmInsId(), Constants.SYSFILE_TYPE_ACTUAL);
            this.updateSysFiles(form.getEstimatedFiles(), form.getPmInsId(), Constants.SYSFILE_TYPE_ESTIMATED);
            UsPmInstence us = usPmInstenceService.findByPmInsId(form.getPmInsId());
            us.setModifiedTime(LocalDateTime.now());
            usPmInstenceService.update(us);
        }
        return form;
    }


    /**
     * 保存草稿
     *
     * @param source
     * @param currentUserCode
     * @param applicationMaintainForm
     * @return
     */
    @Override
    public JsonResponse saveDraft(String source, String currentUserCode, ApplicationMaintainForm applicationMaintainForm) {
        /**准备操作参数**/
        Map<String, Object> optLogParam = Maps.newHashMap();

        optLogParam.put("source", source);
        optLogParam.put("currentUserCode", currentUserCode);
        optLogParam.put("currencyFrom", applicationMaintainForm.toString());
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode);
            if (returnObj != null) {
                return returnObj;
            }
            // 保存草稿
            if (org.apache.commons.lang3.StringUtils.isEmpty(applicationMaintainForm.getId())) {
                // 保存业务单据信息
                this.saveMaintainForm(applicationMaintainForm);
                // 更新草稿
            } else {
                //更新业务单据信息
                this.updateBusinessData(applicationMaintainForm);
            }

        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(optLogParam);
        }
        return JsonResponse.success(applicationMaintainForm, Constants.MESSAGE_SUCCESS);
    }


    //草稿箱保存
    public Map<String, Object> saveMaintainForm(ApplicationMaintainForm model) {
        Map<String, Object> result = Maps.newHashMap();
        IUser iuser = SecurityUtils.getCurrentUser();
        UsPmInstence usPmInstence = new UsPmInstence();
        JsonResponse jsonResponse = null;
//        Map<String, String> processM = this.getProcess(String.valueOf(model.getType()));
//        String processN = processM.get("processN");
        String processT = "D";
        usPmInstence.setPmInsType(processT);

        try {
            /**保存主单据**/
            if (org.apache.commons.lang3.StringUtils.isEmpty(model.getId())) {
//
                String pmInsId = usPmInstence.getPmInsType() + String.valueOf(IdGenerator.idWorker.nextId());//获取到pmInsId
                usPmInstence.setPmInsId(pmInsId);

                //usPmInstence.setPmInsType(Constants.FLOW_APPLY_TYPE);
                //usPmInstence.setPmInsId(NumRuleUtil.getApplyPmInsId());
                usPmInstence.setBelongCompanyName(iuser.getBelongCompanyName());
                usPmInstence.setBelongCompanyCode(iuser.getBelongCompanyCode());
                usPmInstence.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                usPmInstence.setBelongDepartmentName(iuser.getBelongDepartmentName());
                usPmInstence.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                usPmInstence.setBelongOrgCode(iuser.getBelongOrgCode());
                usPmInstence.setBelongOrgName(iuser.getBelongOrgName());
                usPmInstenceService.insert(usPmInstence);
                /**保存请示信息数据**/
                String usPmId = usPmInstence.getId();
                if (StringUtil.isNotEmpty(usPmId)) {
                    model.setPmInsId(usPmInstence.getPmInsId());
                    model.setModifiedTime(LocalDateTime.now());
                    model.setBelongCompanyCode(iuser.getBelongCompanyCode());
                    model.setBelongCompanyName(iuser.getBelongCompanyName());
                    model.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                    model.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                    model.setBelongDepartmentName(iuser.getBelongDepartmentName());
                    model.setBelongOrgCode(iuser.getBelongOrgCode());
                    model.setBelongOrgName(iuser.getBelongOrgName());
                    /** 更新附件 **/
                    //this.updatePmInsId(currencyFrom);
                    model = this.insert(model);
                }
                if (model != null) {
                    String busId = model.getId();
                    //保存调查项数据
                    List<ApplicationAndEquipment> busUseAndCountList = model.getEquipmentList();
                    for (ApplicationAndEquipment busUseAndCount : busUseAndCountList) {
                        busUseAndCount.setFormId(busId);
                        busUseAndCount.setEnabled(true);
                        //主单据id
                        //    busUseAndCount.setPmInsId(usPmInstence.getPmInsId());
                        busUseAndCount.setCreatedTime(model.getCreatedTime());
                        busUseAndCount.setModifiedTime(model.getModifiedTime());
                        ApplicationAndEquipment busUseAndCounts = applicationAndEquipmentService.insert(busUseAndCount);
                        if (busUseAndCounts != null) {
                            jsonResponse = JsonResponse.success("新增数据成功！");
                        } else {
                            jsonResponse = JsonResponse.fail("新增数据失败！");
                        }
                    }


                }
            }
        } catch (Exception e) {
            return result;
        }
        return result;

    }

    /**
     * 修改业务单据信息
     *
     * @param model
     * @return
     */
    private void updateBusinessData(ApplicationMaintainForm model) {
        ApplicationMaintainForm businessEntertainmentPurchaseModel1 = this.findById(model.getId());
        CustomBeanUtil.copyPropertiesIgnoreNull(model, businessEntertainmentPurchaseModel1);

        //更新附件
        //this.updatePmInsId(currencyFrom);
        // 更新表单基础数据
        model = this.update(model);

        updateBusUseAndCountForm(model);

        //更新主单据UsPmInstence
        UsPmInstence usPmInstence = usPmInstenceService.findByPmInsId(model.getPmInsId());
        usPmInstence.setModifiedTime(LocalDateTime.now());
        usPmInstenceService.update(usPmInstence);

    }


    public JsonResponse updateBusUseAndCountForm(ApplicationMaintainForm businessEntertainmentPurchase) {
        JsonResponse jsonResponse = null;
        try {
            if (businessEntertainmentPurchase != null) {
                //cTable组件删除数据,获取视频调查表中要删除的数据
                //不懂
                List<ApplicationAndEquipment> busUseAndCountResultByFind = applicationAndEquipmentService.getEquipmentList(businessEntertainmentPurchase.getId());
                //获取前端调查项数据
                List<ApplicationAndEquipment> busUseAndCountInfoList = businessEntertainmentPurchase.getEquipmentList();
                for (ApplicationAndEquipment busUseAndCount : busUseAndCountResultByFind) {
                    //数据库查出来的数据与前端传来的数据进行对比，如果前端删除了某一条就去更新数据库
                    Optional<ApplicationAndEquipment> first = busUseAndCountInfoList.stream().filter(m -> busUseAndCount.getId().equals(m.getId())).findFirst();
                    if (!first.isPresent()) {
//                        Integer  integer = busUseAndCountRepository.updateBusUseAndCountInfo(busUseAndCount.getId());
                        //applicationAndEquipmentService.delete(busUseAndCount);
                        applicationAndEquipmentService.deleteById(busUseAndCount.getId());
                    }

                }
                ApplicationAndEquipment busUseAndCount = null;
                for (ApplicationAndEquipment busUseAndCounts : busUseAndCountInfoList) {
                    if (busUseAndCounts.getId() != null) {
                        //更新已修改的调查项
                        busUseAndCount = applicationAndEquipmentService.update(busUseAndCounts);
                    } else {
                        CustomBeanUtil.copyPropertiesIgnoreNull(busUseAndCounts, businessEntertainmentPurchase);
//                        busUseAndCounts.setPmInsId(businessEntertainmentPurchase.getPmInsId());
//                        busUseAndCounts.setBusId(businessEntertainmentPurchase.getId());
//                        busUseAndCounts.setUserCode(businessEntertainmentPurchase.getUserCode());
//                        busUseAndCounts.setTrueName(businessEntertainmentPurchase.getTrueName());
//                        busUseAndCounts.setEnabled(true);
//                        busUseAndCounts.setPmInsId(businessEntertainmentPurchase.getPmInsId());
//                        busUseAndCounts.setBelongCompanyCode(businessEntertainmentPurchase.getBelongCompanyCode());
//                        busUseAndCounts.setBelongCompanyName(businessEntertainmentPurchase.getBelongCompanyName());
//                        busUseAndCounts.setBelongDepartmentCode(businessEntertainmentPurchase.getBelongDepartmentCode());
//                        busUseAndCounts.setBelongDepartmentName(businessEntertainmentPurchase.getBelongDepartmentName());
//                        busUseAndCounts.setBelongOrgCode(businessEntertainmentPurchase.getBelongOrgCode());
//                        busUseAndCounts.setBelongOrgName(businessEntertainmentPurchase.getBelongOrgName());
//                        busUseAndCounts.setCreateTime(businessEntertainmentPurchase.getCreateTime());
//                        busUseAndCounts.setModifiedTime(businessEntertainmentPurchase.getModifiedTime());
//                        busUseAndCounts.setBelongCompanyTypeDictValue(businessEntertainmentPurchase.getBelongCompanyTypeDictValue());
//                        busUseAndCount = busUseAndCountService.insert(busUseAndCounts);
                    }
                }

                if (busUseAndCount != null) {
                    jsonResponse = JsonResponse.success("更新数据成功！");
                } else {
                    jsonResponse = JsonResponse.fail("更新数据成功！");
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonResponse;
    }


//    public JsonResponse downloadFormByDoc(Long processInstId, String workFlag, String source, String userCode, String location) throws IOException {
//        JsonResponse response = this.getFormDetail(processInstId, workFlag, source, userCode, location);
//        ApplicationMaintainForm form = (ApplicationMaintainForm) response.getData();
//        this.creatDoceByForm(form);
//        return JsonResponse.defaultSuccessResponse();
//    }

//    private boolean creatDoceByForm(ApplicationMaintainForm form) throws IOException {
//        ArrayList<Object> baseInfoTable = this.creatBaseInfo(form);
//        File file = new File("D:\\testModel.txt");
//        Map<String, Object> datas = new HashMap<String, Object>(){{
//            put("name", "老王");
//            put("age", "80");
//            put("sex","男");
//            put("baseInfoTable", new TableRenderData(new ArrayList<RenderData>(), baseInfoTable, "no datas", 8600));
//        }};
//
//        //读取模板，进行渲染
//        XWPFTemplate doc = XWPFTemplate
//                .create("D:/test.docx");
//        RenderAPI.render(doc, datas);
//
//        //输出渲染后的文件
//        FileOutputStream out = new FileOutputStream("D:/DATA/output.docx");
//        doc.write(out);
//        out.flush();
//        out.close();
//        return false;
//    }

    /**
     * 根据pmInsId获取设备信息
     *
     * @param pmInsId
     * @return
     */
    @Override
    public ApplicationMaintainForm findInfoByPmInsId(String pmInsId) {
        return maintainFormRepository.findInfoByPmInsId(pmInsId);
    }

    /**
     * 生成基本信息数据
     *
     * @param form
     * @return
     */
    private ArrayList<Object> creatBaseInfo(ApplicationMaintainForm form) {
        ArrayList<Object> baseInfoTable = Lists.newArrayList();
        baseInfoTable.add("工单编号;" + form.getUniqueNum() + ";工单类型;" + form.getOrderTypeName());
        baseInfoTable.add("申请部门;" + form.getApplyUnitName() + ";申请人;" + form.getApplyUser());
        baseInfoTable.add("申请人电话;" + form.getApplyPhone() + ";期望完成日期;" + form.getApplyDate());
        baseInfoTable.add("工单名称;" + form.getTitle());
        return baseInfoTable;
    }

    /**
     * 判断申请人每月的维修次数是否已达上限
     *
     * @param equipmentList
     */
    @Override
    public JsonResponse JudgeEquipmentNum(List<ApplicationAndEquipment> equipmentList) {
        JsonResponse jsonResponse = new JsonResponse();
        String errorMessage = "";
        boolean flag = false;
        for (ApplicationAndEquipment model : equipmentList) {

            Integer num = applicationAndEquipmentRepository.findAllByOneByNameAndId(model.getEquipmentId(), model.getPrincipalName());

            if (num != null & num >= 3) {
                flag = true;
                errorMessage = errorMessage + "," + model.getEquipmentName();
            }

        }
        if (flag) {
            errorMessage = errorMessage.substring(1);
            jsonResponse.setErrcode(1);
            jsonResponse.setStatus(203);
            jsonResponse.setMessage("(" + errorMessage + ")本月已申请维修3次，本月不可再申请。");
        } else {
            jsonResponse.setErrcode(0);


        }
        return jsonResponse;
    }
//
//     Page<ActBusinessStatus> getAreadyDoneByUserIdSubFlowPage(Map<String,String > doneUserParam, Pageable pageable) {
//        Page<ActBusinessStatus> pages = null;
//        String assistant = (String)doneUserParam.get("assistant");
//        String dynamicWhere = (String)doneUserParam.get("title");
//        String pmInsType = (String)doneUserParam.get("pmInsType");
//        new StringBuilder();
//
//        try {
//            if (org.apache.commons.lang3.StringUtils.isEmpty(dynamicWhere)) {
//                dynamicWhere = "";
//            }
//
//            List<String> inWheres = new ArrayList();
//            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pmInsType)) {
//                inWheres.addAll(Arrays.asList(pmInsType.split(",")));
//            }
//
//            pages = this.actBusinessStatusMapper.getByAreadyDoneAssistantSubFlowPage(assistant, dynamicWhere, inWheres, pageable);
//            Iterator var9 = pages.getContent().iterator();
//
//            while(var9.hasNext()) {
//                ActBusinessStatus actBusinessStatus = (ActBusinessStatus)var9.next();
//                Long parentProcessInstId = actBusinessStatus.getParentProcId();
//                Long processInstId = actBusinessStatus.getProcessInstId();
//                if (!parentProcessInstId.equals(0)) {
//                    processInstId = actBusinessStatus.getProcessInstId();
//                    List<ActBusinessStatus> statusList = this.actBusinessStatusMapper.getByProcessInstIdDoneNew(processInstId);
//                    if (!statusList.isEmpty() && statusList.size() > 0) {
//                        processInstId = ((ActBusinessStatus)statusList.get(0)).getProcessInstId();
//                    }
//                }
//
//                WfWorkItemModel wfWorkItemModel = (WfWorkItemModel)this.wfWorkItemModelService.queryAredayTodoByParticipantAndProInsId(processInstId, assistant).get(0);
//                actBusinessStatus.setWorkItemId(wfWorkItemModel.getWorkItemId());
//                actBusinessStatus.setActivityDefId(wfWorkItemModel.getActivityDefId());
//                actBusinessStatus.setActivityInstName(wfWorkItemModel.getActivityInstName());
//                actBusinessStatus.setParticipant(assistant);
//                actBusinessStatus.setAssistant(wfWorkItemModel.getAssistant());
//                actBusinessStatus.setWorkItemStartTime(wfWorkItemModel.getStartTime());
//                actBusinessStatus.setWorkItemEndTime(wfWorkItemModel.getEndTime());
//                actBusinessStatus.setPreviousAssistant(wfWorkItemModel.getParticipant());
//                actBusinessStatus.setPreviousAssistantName(wfWorkItemModel.getPartiName());
//                String processType = actBusinessStatus.getReceiptCode().replaceAll("[^a-z^A-Z]", "");
//                actBusinessStatus.setPmInsType(processType);
//            }
//        } catch (Exception var15) {
//            BpsWorkFlowBusinessException.printException(var15);
//        }
//
//        return pages;
//    }


    /**
     * 废除草稿
     *
     * @return
     */
    @Override
    @Transactional
    public JsonResponse deleteDraft(String source, String currentUserCode, String pmInsId, ApplicationMaintainForm model) {
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/deleteDraft";
        String params = "pmInsId=" + pmInsId + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            // 删除主单据数据
            UsPmInstence pmInstence = usPmInstenceService.findByPmInsId(pmInsId);
            usPmInstenceService.deleteById(pmInstence.getId());
            // 删除业务单据数据
            //this.delete(model);
            this.deleteById(model.getId());
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(null, Constants.MESSAGE_SUCCESS);
    }

    /**
     * 设备维修预算使用情况
     *
     * @param belongCompanyCode
     * @return
     */
    @Override
    public JsonResponse findBudgetMoney(String belongCompanyCode, String pmInsId, String source, String currentUserCode) {
        Map<String, Object> map = CollectionUtil.newHashMap();
        Map<String, Object> map3 = CollectionUtil.newHashMap();

        belongCompanyCode = "4772338661636601428";
        try {
            if (Constants.MOBILE.equals(source)) {
                loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
            }
            IUser user = SecurityUtils.getCurrentUser();

//            //定义时间类
//            Calendar calendar = Calendar.getInstance();
//            //获取当前年份
//            String year = String.valueOf(calendar.get(Calendar.YEAR));
            ApplicationMaintainForm one = super.findOne(Specifications.<ApplicationMaintainForm>and().eq("pmInsId", pmInsId).build());
            String year = String.valueOf(one.getCreatedTime().getYear());
            map.put("thisYear", year);
            //获取地市或省当前年份的预算
            map = maintainFormRepository.findBudgetMoney(belongCompanyCode, year);

            if (CollectionUtil.isNotEmpty(map)) {
                //获取地市或省名称name
                String belongCompanyName = cn.hutool.core.map.MapUtil.getStr(map, "BELONG_COMPANY_NAME");
                //获取预算金额
                String totalAmountBybs = cn.hutool.core.map.MapUtil.getStr(map, "TOTAL_AMOUNT_BYBS");
                //获取实际金额
                String totalActualBybs = cn.hutool.core.map.MapUtil.getStr(map, "TOTAL_ACTUAL_BYBS");
                //获取可用金额
                String usableAmountBybs = cn.hutool.core.map.MapUtil.getStr(map, "USABLE_AMOUNT_BYBS");

                String totalLockBybs = cn.hutool.core.map.MapUtil.getStr(map, "TOTAL_LOCK_BYBS");

                String belongCompanyCodes = cn.hutool.core.map.MapUtil.getStr(map, "BELONG_COMPANY_CODE");

                //实际发生金额占总金额的比例
                float ave = 0.f;
                double ave1 = Double.parseDouble(totalActualBybs) / Double.parseDouble(totalAmountBybs) * 100;
                ave = (float) ave1;
                String avec = String.format("%.2f", ave);
                /**
                 * 处理申请部门累计使用金额
                 */
                Map<String, Object> map1 = maintainFormRepository.findApplicationAndId(user.getBelongDepartmentCode(), year);
                String actualAmount = null;
                if (CollectionUtil.isNotEmpty(map1)) {
                    //获取部门累计使用金额
                    actualAmount = cn.hutool.core.map.MapUtil.getStr(map1, "ACTUAL_AMOUNT");
                    if (actualAmount == null) {
                        actualAmount = "0";
                    }
                }
                //部门累计金额占预算实际金额的比例
                float aveA = 0.f;
                double aveA1 = Double.parseDouble(actualAmount) / Double.parseDouble(totalActualBybs) * 100;
                aveA = (float) aveA1;
                String aveAc = String.format("%.2f", aveA);
                if (aveAc.equals("NaN")) {
                    aveAc = "0";
                }
                map3.put("name", year + "年" + belongCompanyName + "设备维修预算");
                map3.put("belongCompanyName", belongCompanyName);
                map3.put("belongCompanyCode", belongCompanyCodes);
                map3.put("totalActualBybs", totalActualBybs);
                map3.put("totalAmountBybs", totalAmountBybs);
                map3.put("totalLockBybs", totalLockBybs);
                map3.put("usableAmountbybs", usableAmountBybs);
                map3.put("avec", avec);
                map3.put("departmentName", user.getBelongCompanyName() + "\\" + user.getBelongDepartmentName());
                map3.put("actualAmountTo", actualAmount);
                map3.put("aveAc", aveAc + "%");
                map3.put("thisYear", year);
                map = map3;
            } else {

                /**
                 * 处理申请部门累计使用金额
                 */
                Map<String, Object> map1 = maintainFormRepository.findApplicationAndId(user.getBelongDepartmentCode(), year);
                String actualAmount = null;
                if (CollectionUtil.isNotEmpty(map1)) {
                    //获取部门累计使用金额
                    actualAmount = cn.hutool.core.map.MapUtil.getStr(map1, "ACTUAL_AMOUNT");
                    if (actualAmount == null) {
                        actualAmount = "0";
                    }
                }
                String totalActualBybs = "0";
                //部门累计金额占预算实际金额的比例
                float aveA = 0.f;
                double aveA1 = Double.parseDouble(actualAmount) / Double.parseDouble(totalActualBybs) * 100;
                aveA = (float) aveA1;
                String aveAc = String.format("%.2f", aveA);
                if (aveAc.equals("NaN")) {
                    aveAc = "0";
                }
                map.put("name", "--");
                map.put("belongCompanyName", "--");
                map.put("belongCompanyCode", "--");
                map.put("totalActualBybs", "--");
                map.put("totalAmountBybs", "--");
                map.put("totalLockBybs", "--");
                map.put("usableAmountbybs", "--");
                map.put("avec", "--");
                map.put("departmentName", user.getBelongCompanyName() + "\\" + user.getBelongDepartmentName());
                map.put("actualAmountTo", actualAmount);
                map.put("aveAc", aveAc + "%");
                map.put("thisYear", year);
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        if (null == map.get("thisYear")) {
            //定义时间类
            Calendar calendar = Calendar.getInstance();
            //获取当前年份
            String year = String.valueOf(calendar.get(Calendar.YEAR));
            map.put("thisYear", year);
        }
        log.warn("11111111111111111111111111111" + map);
        return JsonResponse.success(map);
    }

    /**
     * 设备维修汇总情况
     *
     * @param uniqueNum
     * @return
     */
    @Override
    public JsonResponse findFormAdd(String uniqueNum, String pmInsId, String orderTypeValue, Integer page, Integer rows, Integer size, String source, String currentUserCode, String belongDepartmentCode) {
        List<Map<String, Object>> list = new ArrayList<>();
        //Page<List<Map<String, Object>>> page1 = null;
        try {

            if (Constants.MOBILE.equals(source)) {
                loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
            }


//            //定义时间类
//            Calendar calendar = Calendar.getInstance();
//            //获取当前年份
//            String year = String.valueOf(calendar.get(Calendar.YEAR));
            ApplicationMaintainForm one = super.findOne(Specifications.<ApplicationMaintainForm>and().eq("pmInsId", pmInsId).build());
            String year = String.valueOf(one.getCreatedTime().getYear());

            //获取设备编号
            String[] split = uniqueNum.split(",");
            for (String uniqueNums : split) {
                Map<String, Object> map = CollectionUtil.newHashMap();
                //本年度当前申请设备维修次数
                /*   String numbe = maintainFormRepository.findFormAndNumber(uniqueNums, year);*/


                 /* String numbe = null ;
                    if (CollectionUtil.isNotEmpty(mapNumbe)){
                        numbe =  cn.hutool.core.map.MapUtil.getStr(mapNumbe, "numbe");
                    }*/

                //本年度当前设备维修金额
                List<ApplicationAndEquipment> andEquipments = applicationAndEquipmentRepository.findApplicationAndFormId(uniqueNums, year, belongDepartmentCode);
                List<String> fromIds = new ArrayList<>();
                if (andEquipments.size() > 0) {
                    for (ApplicationAndEquipment applicationAndEquipment : andEquipments) {
                        fromIds.add(applicationAndEquipment.getFormId());
                    }

                }
                if (orderTypeValue.equals("0")) {
                    if (CollectionUtil.isNotEmpty(andEquipments)) {
                        String numbe = maintainFormRepository.findFormAndNumber(uniqueNums, year);
                        Map<String, Object> map1 = maintainFormRepository.findFormAndjine(uniqueNums, year);
                        String actualAmount = cn.hutool.core.map.MapUtil.getStr(map1, "actualAmount");
                        if (StringUtils.isEmpty(actualAmount)) {
                            actualAmount = "0";
                        }
                        List<ApplicationMaintainForm> applicationAndFormId = maintainFormRepository.findApplication(andEquipments.get(0).getFormId());
                        Map<String, Object> mapynt = maintainFormRepository.findFormAndjineAndY(applicationAndFormId.get(0).getBelongDepartmentCode(), year);
                        String Y = null;
                        if (CollectionUtil.isNotEmpty(mapynt)) {
                            Y = cn.hutool.core.map.MapUtil.getStr(mapynt, "Y");
                        } else {
                            Y = "0";
                        }
                        float ave = 0.f;
                        double ave1 = Double.parseDouble(actualAmount) / Double.parseDouble(Y) * 100;
                        ave = (float) ave1;
                        String avec = String.format("%.2f", ave);

                        if (avec.equals("NaN")) {
                            avec = "0";
                        }
                        //资产标签号
                        map.put("AssetTagNum", andEquipments.get(0).getAssetTagNum());
                        //设备名称
                        map.put("equipmentName", andEquipments.get(0).getEquipmentName());
                        //申请部门
                        map.put("ApplyUnitName", applicationAndFormId.get(0).getApplyUnitName());
                        //维修次数
                        map.put("number", numbe);
                        //累计维修金额
                        map.put("actualAmount", actualAmount);
                        //占比
                        map.put("avec", avec + "%");
                        list.add(map);
                    }
                } else {
                    if (CollectionUtil.isNotEmpty(andEquipments)) {
                        List<ApplicationMaintainForm> applicationAndFormIdS = maintainFormRepository.findApplicationS(andEquipments.get(0).getFormId());

                        String numbesr = maintainFormRepository.findFormAndNumbers(uniqueNums, year);
                        //耗材领用2
                        //资产标签号
                        map.put("AssetTagNum", andEquipments.get(0).getAssetTagNum());
                        //设备名称
                        map.put("equipmentName", andEquipments.get(0).getEquipmentName());
                        //申请部门
                        map.put("ApplyUnitName", applicationAndFormIdS.get(0).getApplyUnitName());
                        //维修次数
                        map.put("numbe", numbesr);
                        list.add(map);
                    }

                }
            }

         /*   if (list != null && list.size() > 0) {
                int relustSize = list.size();
                Pageable pageable1 = getPageable(page, size, null, null);
                List<Map<String, Object>> listPart = PageTool.pagination(list, page, size);
                page1 = new PageImpl(listPart, pageable1, relustSize);
            }*/

        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return JsonResponse.success(list);
    }

    /**
     * 本年度部门内
     *
     * @param applyUnitname
     * @return
     */
    @Override
    public JsonResponse findFormAdds(String applyUnitname, String pmInsId, String orderTypeValue, Integer page, Integer rows, Integer size, String source, String currentUserCode, String belongDepartmentCode) {
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> map = CollectionUtil.newHashMap();
        Page<List<Map<String, Object>>> page1 = null;
//        //定义时间类
//        Calendar calendar = Calendar.getInstance();
//        //获取当前年份
//        String year = String.valueOf(calendar.get(Calendar.YEAR));
        ApplicationMaintainForm one = super.findOne(Specifications.<ApplicationMaintainForm>and().eq("pmInsId", pmInsId).build());
        String year = String.valueOf(one.getCreatedTime().getYear());
        try {

            if (Constants.MOBILE.equals(source)) {
                loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
            }

            //设备维修
            if (orderTypeValue.equals("0")) {
                List<String> assetTagNum = applicationAndEquipmentRepository.finApplyUnitAddName(applyUnitname, year);
                if (CollectionUtil.isNotEmpty(assetTagNum)) {
                    for (String assetTagNums : assetTagNum) {
                        if (assetTagNums == null) {
                            return JsonResponse.success(list);
                        }
                        Map<String, Object> map1 = CollectionUtil.newHashMap();
                        /* list = this.findFormAdd(applicationAndEquipment.getUniqueNum(), orderTypeValue, page, rows, size);*/
                        List<ApplicationAndEquipment> listeq = applicationAndEquipmentRepository.findAssetTagNumandEquipments(assetTagNums);
                        String numbe = applicationAndEquipmentRepository.findNumer(assetTagNums, year);

                        String actualAmounts = applicationAndEquipmentRepository.findApplicationAndForm(assetTagNums);
                        String actualAmount = null;
                        if (StringUtils.isEmpty(actualAmounts)) {
                            actualAmount = "0";
                        } else {
                            actualAmount = actualAmounts;
                        }

                        String avc = applicationAndEquipmentRepository.findAvc(applyUnitname);

                        String avcs = null;
                        if (StringUtils.isEmpty(avc)) {
                            avcs = "0";
                        } else {
                            avcs = avc;
                        }

                        float ave = 0.f;
                        double ave1 = Double.parseDouble(actualAmount) / Double.parseDouble(avcs) * 100;
                        ave = (float) ave1;
                        String avec = String.format("%.2f", ave);

                        if (avec.equals("NaN")) {
                            avec = "0";
                        }

                        //资产标签号
                        map1.put("AssetTagNum", assetTagNums);
                        //设备名称
                        map1.put("equipmentName", listeq.get(0).getEquipmentName());
                        //申请部门
                        map1.put("ApplyUnitName", applyUnitname);
                        //维修次数
                        map1.put("number", numbe);

                        map1.put("actualAmount", actualAmount);

                        //占比
                        map1.put("avec", avec + "%");


                        list.add(map1);
                    }

                }
            } else {
                //耗材领用
                List<String> assetTagNum = applicationAndEquipmentRepository.finApplyUnitAddNameHs(applyUnitname, year);
                if (CollectionUtil.isNotEmpty(assetTagNum)) {
                    for (String assetTagNums : assetTagNum) {
                        if (assetTagNums == null) {
                            return JsonResponse.success(list);
                        }
                        Map<String, Object> map2 = CollectionUtil.newHashMap();
                        List<ApplicationAndEquipment> listeq = applicationAndEquipmentRepository.findAssetTagNumandEquipments(assetTagNums);
                        String numbe = applicationAndEquipmentRepository.findNumerS(assetTagNums, year);
                        //资产标签号
                        map2.put("AssetTagNum", assetTagNums);
                        //设备名称
                        map2.put("equipmentName", listeq.get(0).getEquipmentName());
                        //申请部门
                        map2.put("ApplyUnitName", applyUnitname);
                        //维修次数
                        map2.put("numbe", numbe);
                        list.add(map2);
                    }

                }

            }

            if (list != null && list.size() > 0) {
                int relustSize = list.size();
                Pageable pageable1 = getPageable(page, size, null, null);
                List<Map<String, Object>> listPart = PageTool.pagination(list, page, size);
                page1 = new PageImpl(listPart, pageable1, relustSize);
            }

        } catch (Exception e) {
            Exceptions.printException(e);
        }
        //判断如果是客户端就不分页
        if (Constants.MOBILE.equals(source)) {
            return JsonResponse.success(list);
        }
        return JsonResponse.success(page1);

    }

    /**
     * 查询详情工单
     *
     * @param assetTagNum
     * @return
     */
    @Override
    public JsonResponse findFormDetail(String assetTagNum, String orderTypeValue, Integer page, Integer size, Map<String, Object> params, String source, String currentUserCode) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        Page<List<Map<String, Object>>> page1 = null;

        try {
            if (Constants.MOBILE.equals(source)) {
                loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
            }

            String startTime = cn.hutool.core.map.MapUtil.getStr(params, "startTime");
            String endTime = cn.hutool.core.map.MapUtil.getStr(params, "endTime");
            String stateNames = cn.hutool.core.map.MapUtil.getStr(params, "stateName");

            Map<String, Object> paramBobys = CollectionUtil.newHashMap();
            StringBuilder baseSql = new StringBuilder("select t4.* ,t3.*,to_char(t3.created_time, 'yyyy-mm-dd') as created_times" +
                    "                     from ACT_BUSINESS_STATUS t4," +
                    "                          (select t1.title," +
                    "                                  t1.unique_num," +
                    /*      "                                  to_char(t1.created_time, 'yyyy-mm-dd') as created_time," +*/
                    "                                  t1.created_time," +
                    "                                  t1.actual_amount," +
                    "                                  t1.pm_ins_id," +
                    "                                  t1.id  as  formId," +
                    "                                  t1.creator" +
                    "                             from US_APPLICATION_MAINTAIN_FORM t1," +
                    "                                  (select t.*" +
                    "                                     from US_APPLICATION_AND_EQUIPMENT t");
                 /*   "                                    where t.asset_tag_num =:assetTagNum ) t2" +
                    "                            where t1.id = t2.form_id" +
                    "                              and t1.enabled = '1') t3" +
                    "                      where t4.receipt_code = t3.pm_ins_id" +
                    "                      and t4.enabled = '1'"
*/

          /*  //上传开始时间
            if (StrUtil.isNotEmpty(startTime)) {
                baseSql.append(" and to_char(t3.created_time, 'yyyy-mm-dd')  >=:startTime");
                paramBobys.put("startTime", startTime);
            }
            //上传结束时间
            if (StrUtil.isNotEmpty(endTime)) {
                baseSql.append(" and to_char(t3.created_time, 'yyyy-mm-dd')  <=:endTime");
                paramBobys.put("endTime", endTime);
            }*/
            //所属模块
            if (StrUtil.isNotEmpty(assetTagNum)) {
                baseSql.append("  where t.asset_tag_num =:assetTagNum");
                paramBobys.put("assetTagNum", assetTagNum);
            }
            baseSql.append(" ) t2   where t1.id = t2.form_id    and t1.enabled = '1'");
            //类型
            if (StrUtil.isNotEmpty(orderTypeValue)) {
                baseSql.append("  and t1.order_type_value =:orderTypeValue");
                paramBobys.put("orderTypeValue", orderTypeValue);
            }
            baseSql.append(") t3   where t4.receipt_code = t3.pm_ins_id   and t4.enabled = '1'");


            //上传开始时间
            if (StrUtil.isNotEmpty(startTime)) {
                baseSql.append(" and to_char(t3.created_time, 'yyyy-mm-dd')  >=:startTime");
                paramBobys.put("startTime", startTime);
            }
            //上传结束时间
            if (StrUtil.isNotEmpty(endTime)) {
                baseSql.append(" and to_char(t3.created_time, 'yyyy-mm-dd')  <=:endTime");
                paramBobys.put("endTime", endTime);
            }
            //所属模块
            if (StrUtil.isNotEmpty(stateNames)) {
                baseSql.append("  and t4.current_state =:stateNames");
                paramBobys.put("stateNames", stateNames);
            }

            List<Map<String, Object>> list = MapUtil.formatHumpNameForList(customDynamicWhere.queryNamedParameterForList(baseSql.toString(), paramBobys));
            /*  List<Map<String, Object>> list = maintainFormRepository.finAttelsType(assetTagNum);*/
            if (CollectionUtil.isNotEmpty(list)) {
                for (Map<String, Object> map : list) {
                    String state = cn.hutool.core.map.MapUtil.getStr(map, "currentState");
                    String formid = cn.hutool.core.map.MapUtil.getStr(map, "formid");
                    String pmInsId = cn.hutool.core.map.MapUtil.getStr(map, "pmInsId");
                    String actualAmount = cn.hutool.core.map.MapUtil.getStr(map, "actualAmount");
                    //具体维修金额
                    if (actualAmount == null) {
                        actualAmount = "0";
                    }
                    map.put("actualAmountTo", actualAmount);
                    String stateName = null;
                    List<Map<String, Object>> listWi = maintainFormRepository.findWmodelall(pmInsId);
                    if (CollectionUtil.isNotEmpty(listWi)) {
                        Map<String, Object> map2 = listWi.get(0);
                        String location = cn.hutool.core.map.MapUtil.getStr(map2, "ACTIVITY_DEF_ID");
                        String workItemId = cn.hutool.core.map.MapUtil.getStr(map2, "WORK_ITEM_ID");
                        String processDefName = cn.hutool.core.map.MapUtil.getStr(map2, "PROCESS_DEF_NAME");
                        map.put("activityDefId", location);
                        map.put("workItemId", workItemId);
                        map.put("processDefName", processDefName);
                    }
                  /*  if (state.equals("2")) {
                        stateName = "流转中";
                    } else {
                        stateName = "已归档";
                    }*/
                    map.put("stateName", state);  //流转状态
                    //查询设备数量
                    Map<String, Object> map1 = maintainFormRepository.findFormAndmer(formid);
                    if (CollectionUtil.isNotEmpty(map1)) {
                        String nuber = cn.hutool.core.map.MapUtil.getStr(map1, "nuber");
                        map.put("nuber", nuber);  //数量
                        /*   map.put("current_state", state);  //数量*/
                    }
                    mapList.add(map);
                }
                //分页方法
                if (mapList != null && mapList.size() > 0) {
                    int relustSize = mapList.size();
                    Pageable pageable1 = getPageable(page, size, null, null);
                    List<Map<String, Object>> listPart = PageTool.pagination(mapList, page, size);
                    page1 = new PageImpl(listPart, pageable1, relustSize);
                }
            }

        } catch (Exception e) {
            Exceptions.printException(e);
        }

        return JsonResponse.success(page1);
    }


}
