package com.simbest.boot.nsbgl.equipment.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Maps;
import com.mzlion.core.json.fastjson.JsonUtil;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.nsbgl.equipment.model.*;
import com.simbest.boot.nsbgl.equipment.repository.EquipmentInfoRepository;
import com.simbest.boot.nsbgl.equipment.service.IEquipmentAlterationService;
import com.simbest.boot.nsbgl.equipment.service.IEquipmentBrandService;
import com.simbest.boot.nsbgl.equipment.service.IEquipmentCategoryService;
import com.simbest.boot.nsbgl.equipment.service.IEquipmentInfoService;
import com.simbest.boot.nsbgl.util.Constants;
import com.simbest.boot.nsbgl.util.FileTool;
import com.simbest.boot.nsbgl.wfquey.service.IQueryDictValueService;
import com.simbest.boot.security.*;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.UploadFileResponse;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.util.distribution.id.RedisIdGenerator;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.office.ExcelUtil;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.IteratorUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.PrintWriter;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;


/**
 * @description: 设备管理——电脑终端设备业务接口
 * @author: jingwenhao
 * @date: 2019/3/8 16:02
 * @version: 1.0
 */
@Slf4j
@Service
public class EquipmentInfoServiceImpl extends LogicService<EquipmentInfo, String> implements IEquipmentInfoService {

    private EquipmentInfoRepository equipmentInfoRepository;


    @Autowired
    private ISysFileService fileService;//文件业务处理层

    @Autowired
    private IEquipmentInfoService equipmentInfoService;//文件业务处理层

    @Autowired
    private IQueryDictValueService queryDictValueService;//数据字典业务接口

    @Autowired
    private IEquipmentCategoryService equipmentCategoryService; // 设备类型业务接口

    @Autowired
    private IEquipmentBrandService equipmentBrandService; // 设备品牌业务接口

    @Autowired
    private IEquipmentAlterationService equipmentAlterationService; // 设备信息变更业务接口

    @Autowired
    private UumsSysGroupApi uumsSysGroupApi; // 群组

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi; // 组织
    @Autowired
    private RsaEncryptor encryptor;
    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi; // 人员

    @Autowired
    private RedisIdGenerator idGenerator;

    @Autowired
    public EquipmentInfoServiceImpl(EquipmentInfoRepository repository) {
        super(repository);
        this.equipmentInfoRepository = repository;
    }

    /**
     * 查询设备列表
     *
     * @param page          当前页码
     * @param rows          每页数量
     * @param direction     排序规则（asc/desc）
     * @param properties    排序规则（属性名称）
     * @param equipmentInfo 设备信息
     * @return
     */
    @Override
    public JsonResponse findByAll(int page, int rows, String direction, String properties, EquipmentInfo equipmentInfo) {
        // 排序规则
        if (StringUtils.isEmpty(direction)) {
            direction = "desc";
        }
        if (StringUtils.isEmpty(properties)) {
            properties = "createdTime";
        }
        // 分页对象
        Pageable pageable = this.getPageable(page, rows, direction, properties);
        // 构建查询条件
        Specification<EquipmentInfo> specification = this.buildSpec(equipmentInfo);
        // 获取查询结果
        Page<EquipmentInfo> pages = this.findAll(specification, pageable);

        return JsonResponse.success(pages);
    }

    /**
     * 新增设备信息
     *
     * @param form
     * @return
     */
    @Override
    //@Transactional
    public JsonResponse createEquipmentInfo(EquipmentInfo form) {
        // 资产标签号
        String assetTagNum = form.getAssetTagNum();
        /**
         * 校验是否有未流转，但资产标签号已存在的情况
         */

        boolean isExistTagNum = false;
        if (StringUtils.isNotEmpty(assetTagNum)) {
            // 查询未流转下一步，数据库已有的设备列表
            List<EquipmentInfo> infoList = equipmentInfoRepository.queryListByAssetTagNumAndFormId();
            for (EquipmentInfo info : infoList) {
                if (assetTagNum.equalsIgnoreCase(info.getAssetTagNum())) {
                    isExistTagNum = true;
                    break;
                }
            }
        }
        /*if (isExistTagNum) {
            return JsonResponse.fail("资产标签号重复，请检查数据!");
        }*/
        // 新增时，默认设备状态
        form.setUsageState(Constants.COMMON_STATUS_ENABLED);
        // 新增时，默认设备录入状态 0 导入状态，1 审批中状态，2 生效状态
        form.setStatus(Constants.COMMON_STATUS_DRAFT);
        // 如果设备用途为空，则先查询设备用途数据字典
        if (StringUtils.isEmpty(form.getEquipmentUsageName())) {
            List<SysDictValue> sysDictValueList = queryDictValueService.queryByType(Constants.DICT_TYPE_EQUIPMENTUSAGE);
            for (SysDictValue sysDictValue : sysDictValueList) {
                if (sysDictValue.getValue().equals(form.getEquipmentUsageValue())) {
                    form.setEquipmentUsageName(sysDictValue.getName());
                    break;
                }
            }
        }
            form.setId(null);
            EquipmentInfo equipmentInfo =     insert(form);

        return JsonResponse.success("数据更新成功");
    }

    /**
     * 导入设备信息
     *
     * @return
     * @throws Exception
     */
    @Override
    public void importExcel(HttpServletRequest request, HttpServletResponse response) {
        JsonResponse jsonResponse = JsonResponse.defaultSuccessResponse();
        try {
            response.setContentType("text/html; charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            PrintWriter out = response.getWriter();
            MultipartHttpServletRequest mureq = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> multipartFiles = mureq.getFileMap();
            // 附件excel 文件名
            String originalFilename = "";
            for (MultipartFile uploadfile : multipartFiles.values()) {
                /**
                 * 这里要区分是“外置设备”还是“电脑终端设备”,处理之后返回最终的设备信息集合
                 */
                originalFilename = uploadfile.getOriginalFilename();
                // 设备类型
                String equipmentType = request.getParameter("equipmentType");
                // 设备用途
                String usageType = request.getParameter("usageType");
                // 工单id
                String formId = request.getParameter("formId");

                List<EquipmentInfo> equipmentInfoList;
                // 如果为电脑终端设备
                if (StringUtils.isNotEmpty(equipmentType) && equipmentType.contains(Constants.COMMON_STATUS_0)) {
                    if (StringUtils.isNotEmpty(originalFilename) && originalFilename.contains("电脑终端")) {
                        // 先上传至sys_file表,注意sheetName名要与excel保持一致
                        UploadFileResponse uploadFileResponse = fileService.importExcel(uploadfile, request.getParameter("pmInsType"), request.getParameter("pmInsId"), request.getParameter("pmInsTypePart"), EquipmentDNInfo.class, "设备信息", 2);
                        // 获取excel表格
                        List<EquipmentDNInfo> equipmentDNInfoList = uploadFileResponse.getListData();
                        // 构建设备信息集合
                        Map<String, Object> map = this.buildListByEquipmentDNInfo(equipmentDNInfoList, usageType, formId);
                        equipmentInfoList = (List<EquipmentInfo>) map.get("equipmentInfoList");
                        if (equipmentInfoList == null) {
                            jsonResponse.setMessage(map.get("messageContent").toString());
                        }
                        uploadFileResponse.setListData(equipmentInfoList);
                        jsonResponse.setData(uploadFileResponse);
                    } else {
                        jsonResponse.setMessage("请确认上传的附件是否正确!");
                        jsonResponse.setData(null);
                    }
                }
                // 如果为外置设备
                if (StringUtils.isNotEmpty(equipmentType) && equipmentType.contains(Constants.COMMON_STATUS_1)) {
                    if (StringUtils.isNotEmpty(originalFilename) && originalFilename.contains("外置设备")) {
                        // 先上传至sys_file表,注意sheetName名要与excel保持一致
                        UploadFileResponse uploadFileResponse = fileService.importExcel(uploadfile, request.getParameter("pmInsType"), request.getParameter("pmInsId"), request.getParameter("pmInsTypePart"), EquipmentWZInfo.class, "设备信息", 2);
                        // 获取excel表格
                        List<EquipmentWZInfo> equipmentWZInfoList = uploadFileResponse.getListData();
                        // 构建设备信息集合
                        Map<String, Object> map = this.buildListByEquipmentWZInfo(equipmentWZInfoList, usageType, formId);
                        equipmentInfoList = (List<EquipmentInfo>) map.get("equipmentInfoList");
                        if (equipmentInfoList == null) {
                            jsonResponse.setMessage(map.get("messageContent").toString());
                        }
                        uploadFileResponse.setListData(equipmentInfoList);
                        jsonResponse.setData(uploadFileResponse);
                    } else {
                        jsonResponse.setMessage("请确认上传的附件是否正确!");
                        jsonResponse.setData(null);
                    }
                }
            }
            String result = "<script type=\"text/javascript\">parent.result=" + JacksonUtils.obj2json(jsonResponse) + "</script>";
            out.println(result);
            out.close();
        } catch (Exception e) {
            jsonResponse.setErrcode(-1);
            Exceptions.printException(e);
        }
    }

    /**
     * 保存导入的设备信息
     *
     * @return
     */
    @Override
    //@Transactional
    public JsonResponse saveEquipmentInfoList(EquipmentInfo equipmentInfo) {
        // 获取设备信息集合
        List<EquipmentInfo> equipmentInfoList = equipmentInfo.getEquipmentInfoList();
        try {
            if (equipmentInfoList == null || equipmentInfoList.isEmpty()) {
                return JsonResponse.fail("数据为空，请重新上传文件!");
            }
            // 查询未流转下一步，数据库已有的设备列表
            List<EquipmentInfo> infoList = equipmentInfoRepository.queryListByAssetTagNumAndFormId();
            // 是否存在未流转时，资产标签号重复情况
            boolean isExistTagNum = false;
            // 校验是否有未流转，但资产标签号已存在的情况
            for (EquipmentInfo equipmentInfo1 : equipmentInfoList) {
                if (StringUtils.isNotEmpty(equipmentInfo1.getAssetTagNum())) {
                    for (EquipmentInfo info : infoList) {
                        if (equipmentInfo1.getAssetTagNum().equalsIgnoreCase(info.getAssetTagNum())) {
                            isExistTagNum = true;
                            break;
                        }
                    }
                }
                if (isExistTagNum) break;
            }
            if (isExistTagNum) {
                return JsonResponse.fail("资产标签号不能重复，请检查相关数据!");
            }
            this.saveAll(equipmentInfoList);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return JsonResponse.success(equipmentInfoList);
    }

    /**
     * 流转之后，批量更新设备编号
     *
     * @param formId
     * @return
     */
    @Override
    //@Transactional
    public boolean updateEquipmentList(String formId, String equipmentType, String usageType) {
        // 初始值
        boolean flag = true;
        // 当前用户
        String userName = SecurityUtils.getCurrentUserName();
        try {
            // 查询设备列表
            Specification<EquipmentInfo> specification = Specifications.<EquipmentInfo>and()
                    .eq((StringUtils.isNotEmpty(formId) && StringUtils.isEmpty(usageType)), "applicationFormId", formId)//工单id
                    .eq((StringUtils.isNotEmpty(formId) && StringUtils.isNotEmpty(usageType)), "creator", userName)//创建人
                    .eq((StringUtils.isNotEmpty(formId) && StringUtils.isNotEmpty(usageType)), "applicationFormId", null)//工单id
                    .eq(StringUtils.isNotEmpty(usageType), "equipmentUsageValue", usageType)//设备用途
                    .eq(StringUtils.isNotEmpty(equipmentType), "equipmentType", equipmentType)//设备类型
                    .eq("enabled", Constants.COMMON_STATUS_ENABLED) //状态可用
                    .build();
            Iterable<EquipmentInfo> allNoPage = this.findAllNoPage(specification);
            List<EquipmentInfo> list = IteratorUtils.toList(allNoPage.iterator());
            if (list.isEmpty()) {
                return false;
            }
            String equipmentNumHead;
            // 如果设备为电脑终端设备
            if (equipmentType.equals(Constants.COMMON_STATUS_0)) {
                equipmentNumHead = "DN";
            } else {
                equipmentNumHead = "WZ";
            }
            // 处理设备编号
            for (EquipmentInfo equipmentInfo : list) {
                // 设备唯一编号
                StringBuffer serialNum = new StringBuffer();
                if (StringUtils.isEmpty(equipmentInfo.getUniqueNum())) {
                    // 从redis中获取设备编号
                    serialNum = serialNum.append(equipmentNumHead).append("-").append(idGenerator.getDateId(equipmentNumHead, 5));
                    // 设备唯一编号
                    equipmentInfo.setUniqueNum(serialNum.toString());
                }
                // 审批过程，设备状态为"审批中"
                equipmentInfo.setStatus(Constants.COMMON_STATUS_APPROVAL);
                // 如果手动起草过程，需要关联工单id
                if ((StringUtils.isNotEmpty(formId) && StringUtils.isNotEmpty(usageType))) {
                    equipmentInfo.setApplicationFormId(formId);
                }
                this.update(equipmentInfo);
            }
        } catch (Exception e) {
            flag = false;
            Exceptions.printException(e);
        }
        return flag;
    }


    /**
     * 根据新设备信息，变更原设备信息
     *
     * @param formId 设备信息变更工单Id
     * @return
     */
    @Override
    //@Transactional
    public boolean updateEquipmentList(String formId) {
        if (StringUtils.isEmpty(formId)) {
            return false;
        }
        try {
            // 查询设备变更信息集合
            EquipmentAlteration equipmentAlteration = new EquipmentAlteration();
            equipmentAlteration.setModifyFormId(formId);
            List<EquipmentAlteration> list = equipmentAlterationService.findAllList(equipmentAlteration);
            if (list.isEmpty()) {
                return false;
            }
            // 变更设备信息
            for (EquipmentAlteration alteration : list) {
                EquipmentInfo equipmentInfo = new EquipmentInfo();
                equipmentInfo.setId(alteration.getEquipmentId());

                equipmentInfo.setAssetTagNum(alteration.getAssetTagNumN());//资产标签号（参照资产管理系统）
                equipmentInfo.setBrandValue(alteration.getBrandValueN());//品牌
                equipmentInfo.setBrandName(alteration.getBrandNameN());//品牌
                equipmentInfo.setEquipmentCategoryValue(alteration.getEquipmentCategoryValueN());//设备类型
                equipmentInfo.setEquipmentCategoryName(alteration.getEquipmentCategoryNameN());//设备类型
                equipmentInfo.setEquipmentUsageValue(alteration.getEquipmentUsageValueN());//设备用途
                equipmentInfo.setEquipmentUsageName(alteration.getEquipmentUsageNameN());//设备用途
                equipmentInfo.setEquipmentName(alteration.getEquipmentNameN());//设备名称
                equipmentInfo.setItemModel(alteration.getItemModelN());//规格型号

                equipmentInfo.setMemory(alteration.getMemoryN());//内存，电脑终端设备相关字段
                equipmentInfo.setDiskCapacity(alteration.getDiskCapacityN());//硬盘容量，电脑终端设备相关字段
                equipmentInfo.setOfficeVersionValue(alteration.getOfficeVersionValueN());//操作系统，电脑终端设备相关字段
                equipmentInfo.setOfficeVersionName(alteration.getOfficeVersionNameN());//操作系统，电脑终端设备相关字段
                equipmentInfo.setOsVersionValue(alteration.getOsVersionValueN());//OFFICE版本，电脑终端设备相关字段
                equipmentInfo.setOsVersionName(alteration.getOsVersionNameN());//OFFICE版本，电脑终端设备相关字段

                equipmentInfo.setProducedDate(alteration.getProducedDateN());//设备生产日期
                equipmentInfo.setStartUsingDate(alteration.getStartUsingDateN());//开始启用日期（参照资产管理系统）
                equipmentInfo.setPrincipalName(alteration.getPrincipalNameN());//责任人姓名
                equipmentInfo.setPrincipalUsername(alteration.getPrincipalUsernameN());//责任人OA账号
                equipmentInfo.setMaintenanceEndDate(alteration.getMaintenanceEndDateN());//免费维保结束日期

                equipmentInfo.setEquipmentPrice(alteration.getEquipmentPriceN());//设备价格
                equipmentInfo.setEquipmentSite(alteration.getEquipmentSiteN());//设备地点名称（参照资产管理系统）
                equipmentInfo.setRemark(alteration.getRemarkN());//备注
                update(equipmentInfo);
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            return false;
        }
        return true;
    }

    /**
     * 更新设备信息(设备录入状态为已生效)
     *
     * @param formId                工单id
     * @param commonStatusEfficient 生效状态
     * @return
     */
    @Override
    //@Transactional
    public boolean updateStatus(String formId, Integer commonStatusEfficient) {
        int updateStatus = 0;
        try {
            // 查询设备列表
            Specification<EquipmentInfo> specification = Specifications.<EquipmentInfo>and()
                    .eq((StringUtils.isNotEmpty(formId)), "applicationFormId", formId)//工单id
                    .eq("enabled", Constants.COMMON_STATUS_ENABLED) //状态可用
                    .build();
            Iterable<EquipmentInfo> allNoPage = this.findAllNoPage(specification);
            List<EquipmentInfo> list = IteratorUtils.toList(allNoPage.iterator());
            if (list.isEmpty()) {
                return false;
            }
            /**
             * 构造工单id
             */
            List<String> equipmentIdList = new ArrayList<>();
            for (EquipmentInfo equipmentInfo : list) {
                equipmentIdList.add(equipmentInfo.getId());
            }
            updateStatus = equipmentInfoRepository.updateStatus(equipmentIdList, commonStatusEfficient);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return updateStatus > 0;
    }

    /**
     * 查询责任部门信息
     *
     * @return
     */
    @Override
    public Map<String, Object> findResponsibleOrg() {


        Map<String, Object> map = new HashMap<>();
        String belongCompanyCode = ""; // 所属公司
        String belongCompanyName = "";
        String belongDepartmentCode = ""; // 所属部门
        String belongDepartmentName = "";
        String phone = "";
        String responsibleDepartmentDisplayName = "";
        IUser currentUser = SecurityUtils.getCurrentUser();// 当前用户
        /**
         * 查询定制人员：
         */
        boolean isExit = false;   // 是否是定制人员
        String orgCode = "";
        String orgDisplayName = "";
        List<SysDictValue> sysDictValueList = queryDictValueService.queryByType(Constants.DICT_TYPE_SPEC_ADMINI);
        if (!sysDictValueList.isEmpty()) {
            for (SysDictValue sysDictValue : sysDictValueList) {
                if (sysDictValue.getFlag().equals(currentUser.getBelongOrgCode()) || sysDictValue.getFlag().equals(currentUser.getBelongDepartmentCode())) {
                    isExit = true;
                    orgCode = sysDictValue.getFlag();
                    orgDisplayName = sysDictValue.getDescription();
                    break;
                }
            }
        }

        /**
         * 根据当前登录人所属公司级别，获取组织编码
         */
        // 如果当前登录人所属公司为县公司
        if (currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_COUNTY)) {
            // 获取当前登录人所属公司code,
            belongDepartmentCode = currentUser.getBelongCompanyCode();
            belongDepartmentName = currentUser.getBelongCompanyName();
            phone = currentUser.getPreferredMobile();
            SimpleOrg parentBySon = uumsSysOrgApi.findParentBySon(Constants.APP_CODE, belongDepartmentCode);
            belongCompanyCode = parentBySon.getOrgCode();
            belongCompanyName = parentBySon.getOrgName();
            responsibleDepartmentDisplayName = belongCompanyName + "\\" + belongDepartmentName;
        }
        // 如果当前登录人所属公司为省公司、分公司
        else {
            belongCompanyCode = currentUser.getBelongCompanyCode();
            belongCompanyName = currentUser.getBelongCompanyName();
            phone = currentUser.getPreferredMobile();
            //  定制设备管理员
            if (isExit) {
                belongDepartmentCode = orgCode;
                String[] split = orgDisplayName.split("\\\\");
                // 如果管理员组织为两级结构，如：商丘分公司\\综合部
                if (split.length == 2) {
                    belongDepartmentName = split[1];
                    responsibleDepartmentDisplayName = split[0] + "\\" + belongDepartmentName;
                }
                // 如果管理员组织为两级结构，如：商丘分公司\\综合部\\行政服务中心
                else {
                    belongDepartmentName = split[2];
                    responsibleDepartmentDisplayName = split[0] + "\\" + split[1] + "\\" + belongDepartmentName;

                }
            } else {
                belongDepartmentCode = currentUser.getBelongDepartmentCode();
                belongDepartmentName = currentUser.getBelongDepartmentName();
                phone = currentUser.getPreferredMobile();
                responsibleDepartmentDisplayName = belongCompanyName + "\\" + belongDepartmentName;
            }
        }

        /**
         * 根据当前登录人，校验其是否是设备管理员
         */
        List<SimpleGroup> groupByUsernameNoPage = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
        // 遍历判断是否管理员
        String isAdministratorDepartment = Constants.COMMON_STATUS_0;
        String isAdministratorCompany = Constants.COMMON_STATUS_0;
        for (SimpleGroup simpleGroup : groupByUsernameNoPage) {
            // 如果用户所属群组包含“部门设备管理员-G0115”
            if (simpleGroup.getSid().equals(Constants.EQUIPMENT_ADMINISTRATOR_DEP_GROUP)) {
                isAdministratorDepartment = Constants.COMMON_STATUS_1;
            }
            // 如果用户所属群组包含“公司设备管理员-G0116”
            if (simpleGroup.getSid().equals(Constants.EQUIPMENT_ADMINISTRATOR_BRANCH_GROUP)) {
                isAdministratorCompany = Constants.COMMON_STATUS_1;
            }
//            if (simpleGroup.getSid().equals(Constants.EQUIPMENT_ADMINISTRATOR_BRANCH_GROUP)) {
//                isAdministratorDepartment="";
//                isAdministratorCompany="";
//            }
        }

        map.put("belongCompanyCode", belongCompanyCode);
        map.put("belongCompanyName", belongCompanyName);
        map.put("belongDepartmentCode", belongDepartmentCode);
        map.put("belongDepartmentName", belongDepartmentName);
        map.put("responsibleDisplayName", responsibleDepartmentDisplayName);
        map.put("isAdministratorDepartment", isAdministratorDepartment);
        map.put("isAdministratorCompany", isAdministratorCompany);
        map.put("currentUser", currentUser);
        map.put("phone", phone);
        return map;
    }


    /**
     * 查询责任部门信息
     *
     * @return
     */
    @Override
    public Map<String, Object> findResponsibleOrg(String source, String username) {
        Map<String, Object> map = new HashMap<>();
        String belongCompanyCode = ""; // 所属公司
        String belongCompanyName = "";
        String belongDepartmentCode = ""; // 所属部门
        String belongDepartmentName = "";
        String responsibleDepartmentDisplayName = "";
        if (source.equals("MOBILE")) {
            username = encryptor.decrypt(username);
        }
        SimpleUser currentUser = uumsSysUserinfoApi.findByUsername(username, "nsbgl");
        //IUser currentUser = SecurityUtils.getCurrentUser();// 当前用户
        /**
         * 查询定制人员：
         */
        boolean isExit = false;   // 是否是定制人员
        String orgCode = "";
        String orgDisplayName = "";
        List<SysDictValue> sysDictValueList = queryDictValueService.queryByType(Constants.DICT_TYPE_SPEC_ADMINI);
        if (!sysDictValueList.isEmpty()) {
            for (SysDictValue sysDictValue : sysDictValueList) {
                if (sysDictValue.getFlag().equals(currentUser.getBelongOrgCode()) || sysDictValue.getFlag().equals(currentUser.getBelongDepartmentCode())) {
                    isExit = true;
                    orgCode = sysDictValue.getFlag();
                    orgDisplayName = sysDictValue.getDescription();
                    break;
                }
            }
        }

        /**
         * 根据当前登录人所属公司级别，获取组织编码
         */
        // 如果当前登录人所属公司为县公司
        if (currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_COUNTY)) {
            // 获取当前登录人所属公司code,
            belongDepartmentCode = currentUser.getBelongCompanyCode();
            belongDepartmentName = currentUser.getBelongCompanyName();
            SimpleOrg parentBySon = uumsSysOrgApi.findParentBySon(Constants.APP_CODE, belongDepartmentCode);
            belongCompanyCode = parentBySon.getOrgCode();
            belongCompanyName = parentBySon.getOrgName();
            responsibleDepartmentDisplayName = belongCompanyName + "\\" + belongDepartmentName;
        }
        // 如果当前登录人所属公司为省公司、分公司
        else {
            belongCompanyCode = currentUser.getBelongCompanyCode();
            belongCompanyName = currentUser.getBelongCompanyName();
            //  定制设备管理员
            if (isExit) {
                belongDepartmentCode = orgCode;
                String[] split = orgDisplayName.split("\\\\");
                // 如果管理员组织为两级结构，如：商丘分公司\\综合部
                if (split.length == 2) {
                    belongDepartmentName = split[1];
                    responsibleDepartmentDisplayName = split[0] + "\\" + belongDepartmentName;
                }
                // 如果管理员组织为两级结构，如：商丘分公司\\综合部\\行政服务中心
                else {
                    belongDepartmentName = split[2];
                    responsibleDepartmentDisplayName = split[0] + "\\" + split[1] + "\\" + belongDepartmentName;
                }
            } else {
                belongDepartmentCode = currentUser.getBelongDepartmentCode();
                belongDepartmentName = currentUser.getBelongDepartmentName();
                responsibleDepartmentDisplayName = belongCompanyName + "\\" + belongDepartmentName;
            }
        }

        /**
         * 根据当前登录人，校验其是否是设备管理员
         */
        List<SimpleGroup> groupByUsernameNoPage = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);
        // 遍历判断是否管理员
        String isAdministratorDepartment = Constants.COMMON_STATUS_0;
        String isAdministratorCompany = Constants.COMMON_STATUS_0;
        for (SimpleGroup simpleGroup : groupByUsernameNoPage) {
            // 如果用户所属群组包含“部门设备管理员-G0115”
            if (simpleGroup.getSid().equals(Constants.EQUIPMENT_ADMINISTRATOR_DEP_GROUP)) {
                isAdministratorDepartment = Constants.COMMON_STATUS_1;
            }
            // 如果用户所属群组包含“公司设备管理员-G0116”
            if (simpleGroup.getSid().equals(Constants.EQUIPMENT_ADMINISTRATOR_BRANCH_GROUP)) {
                isAdministratorCompany = Constants.COMMON_STATUS_1;
            }
        }
        map.put("belongCompanyCode", belongCompanyCode);
        map.put("belongCompanyName", belongCompanyName);
        map.put("belongDepartmentCode", belongDepartmentCode);
        map.put("belongDepartmentName", belongDepartmentName);
        map.put("responsibleDisplayName", responsibleDepartmentDisplayName);
        map.put("isAdministratorDepartment", isAdministratorDepartment);
        map.put("isAdministratorCompany", isAdministratorCompany);
        map.put("currentUser", currentUser);
        return map;
    }

    /**
     * 导出设备信息
     *
     * @param form
     * @param response
     * @param request
     * @param sheetName
     * @return
     */
    @Override
    public boolean exportExcel(EquipmentInfo form, HttpServletResponse response, HttpServletRequest request, String sheetName) {
        boolean flag = false;
        String path = request.getServletContext().getRealPath("down");//获取项目动态绝对路径
        LocalDateTime date = LocalDateTime.now();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String fileName = df.format(date) + sheetName + ".xls";
        // 查询数据
        Specification<EquipmentInfo> specification = this.buildSpec(form);
        Iterable<EquipmentInfo> iterable = this.findAllNoPage(specification);
        List<EquipmentInfo> equipmentInfoList = IteratorUtils.toList(iterable.iterator());
        if (!equipmentInfoList.isEmpty()) {
            try {
                // 处理集合数据
                for (EquipmentInfo equipmentInfo : equipmentInfoList) {
                    if ( null != equipmentInfo.getUsageState() && Constants.COMMON_STATUS_UNENABLED.equals( equipmentInfo.getUsageState()) ) {
                        equipmentInfo.setUsageStatusStr("报废");
                    } else {
                        equipmentInfo.setUsageStatusStr("正常");
                    }
                    // 如果为外置设备
                    if ( StrUtil.isNotEmpty(equipmentInfo.getEquipmentType()) && equipmentInfo.getEquipmentType().equals(Constants.COMMON_STATUS_1)) {
                        equipmentInfo.setMemory("---");
                        equipmentInfo.setDiskCapacity("---");
                        equipmentInfo.setOsVersionName("---");
                        equipmentInfo.setOfficeVersionName("---");
                    }
                    equipmentInfo.setCreatorUser(equipmentInfo.getCreator());
                    equipmentInfo.setCreatedDateTime(df.format(equipmentInfo.getCreatedTime()));
                }

                Map<String, Object> map = Maps.newHashMap();
                String targetFileName = path + "\\" + fileName;
                File targetFile = new File(targetFileName);
                //覆盖文件
                FileUtils.touch(targetFile);
                ExcelUtil<EquipmentInfo> exportUtil = new ExcelUtil<EquipmentInfo>(EquipmentInfo.class);
                exportUtil.exportExcel(equipmentInfoList, sheetName, new FileOutputStream(targetFile), null);
                FileTool.download(targetFile.getPath(), response);
                flag = true;
            } catch (Exception e) {
                Exceptions.printException(e);
                flag = false;
            }
        }
        return flag;
    }

    @Override
    //@Transactional
    public JsonResponse updateEquipmentInfo(EquipmentInfo form) {
        if (StringUtils.isNotEmpty(form.getAssetTagNum())) {
            /**
             * 构建查询条件
             */
            Specification<EquipmentInfo> specification = Specifications.<EquipmentInfo>and()
                    .ne(StringUtils.isNotEmpty(form.getId()), "id", form.getId())//ID
                    .eq("status", Constants.COMMON_STATUS_0)//未流转状态
                    .eq("assetTagNum", form.getAssetTagNum())
                    .eq("enabled", Constants.COMMON_STATUS_ENABLED) //状态可用
                    .build();
            Iterable<EquipmentInfo> iterable = this.findAllNoPage(specification);
          /*  List list = IteratorUtils.toList(iterable.iterator());
            if (!list.isEmpty()) {
                return JsonResponse.fail("资产标签号重复，请检查相关数据!");
            }*/
        }

        equipmentInfoRepository.updateForm(form.getId());
        form.setId(null);
        return JsonResponse.success(insert(form));
    }

    /**
     * 根据资产编号查询
     *
     * @param assetTagNum
     * @return
     */
    @Override
    public List<EquipmentInfo> findEnumentInfo(String assetTagNum) {
        return equipmentInfoRepository.findEnumentInfo(assetTagNum);
    }



    /**
     * 构建查询条件
     *
     * @param form
     * @return
     */
    private Specification<EquipmentInfo> buildSpec(EquipmentInfo form) {
        // 查询方式,0:设备录入查询，1:设备生效之后的列表查询
        String queryMode = form.getQueryMode();
        // 资产标签号
        String assetTagNum = form.getAssetTagNum();
        // 设备编号
        String uniqueNum = form.getUniqueNum();
        // 责任人姓名
        String principalName = form.getPrincipalName();
        // 责任人OA账号
        String principalUsername = form.getPrincipalUsername();
        // 设备类型
        String equipmentType = form.getEquipmentType();
        // 设备录入状态
        Integer status = form.getStatus();
        // 设备使用状态
        Integer usageState = form.getUsageState();
        // 设备类型
        String equipmentCategoryValue = form.getEquipmentCategoryValue();
        // 设备名称
        String equipmentName = form.getEquipmentName();
        // 设备关联表单id
        String applicationFormId = form.getApplicationFormId();
        // 设备登记人
        String creator = form.getCreator();
        // 设备地点
        String equipmentSite = form.getEquipmentSite();
        // 设备用途
        String equipmentUsageValue = form.getEquipmentUsageValue();
        // 是否本人登记
        //String isMySelf = form.getIsMySelf();
        // 负责部门
        String responsibleDepartmentCode = form.getResponsibleDepartmentCode();
        // 当前登录人
        IUser currentUser = SecurityUtils.getCurrentUser();
        // 设备登记日期
        String createdDateTime = form.getCreatedDateTime();

        // 当查询方式为“设备生效”列表搜索,处理相关查询条件
        String belongCompanyCode = "";
        String belongDepartmentCode = "";
        String isAdministratorDepartment = "";
        String isAdministratorCompany = "";
        if (queryMode.equals(Constants.COMMON_STATUS_1) || queryMode.equals(Constants.COMMON_STATUS_2)) {
            // 校验是否是管理员
            Map<String, Object> map = this.findResponsibleOrg();
            belongCompanyCode = (String) map.get("belongCompanyCode"); // 负责公司
            belongDepartmentCode = (String) map.get("belongDepartmentCode"); // 负责部门
            isAdministratorDepartment = (String) map.get("isAdministratorDepartment"); // 部门管理员
            isAdministratorCompany = (String) map.get("isAdministratorCompany"); // 公司管理员
            if (queryMode.equals(Constants.COMMON_STATUS_2)) {
                responsibleDepartmentCode = belongDepartmentCode;
            }
        }
        // 处理设备登记日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = null;
        if (StringUtils.isNotEmpty(createdDateTime)) {
            dateTime = LocalDateTime.parse(createdDateTime + " 00:00:00", formatter);
        }

        /**
         * 构建查询条件
         */
        Specification<EquipmentInfo> specification = Specifications.<EquipmentInfo>and()
                .like(StringUtils.isNotEmpty(assetTagNum), "assetTagNum", "%" + assetTagNum + "%")//资产标签号
                .like(StringUtils.isNotEmpty(uniqueNum), "uniqueNum", "%" + uniqueNum + "%")//设备编号
                .like(StringUtils.isNotEmpty(equipmentName), "equipmentName", "%" + equipmentName + "%")//设备名称
                .like(StringUtils.isNotEmpty(principalName), "principalName", "%" + principalName + "%")//责任人姓名
                .like(StringUtils.isNotEmpty(principalUsername), "principalUsername", "%" + principalUsername + "%")//责任人OA账号
                .eq(StringUtils.isNotEmpty(equipmentType), "equipmentType", equipmentType)//设备类型
                .eq(StringUtils.isNotEmpty(equipmentCategoryValue), "equipmentCategoryValue", equipmentCategoryValue)//设备类型
                .eq(StringUtils.isNotEmpty(equipmentUsageValue), "equipmentUsageValue", equipmentUsageValue)//设备使用状态
                .eq(StringUtils.isNotEmpty(equipmentSite), "equipmentSite", equipmentSite)//设备地点
                .eq(StringUtils.isNotEmpty(creator), "creator", creator)//登记人OA账号
                .gt(dateTime != null, "createdTime", dateTime)//登记日期
                .eq(StringUtils.isNotEmpty(responsibleDepartmentCode) && (isAdministratorDepartment.equals(Constants.COMMON_STATUS_1)), "responsibleDepartmentCode", responsibleDepartmentCode)//责任部门
                .eq((null != status), "status", status)//设备录入状态
                .eq((null != usageState && !usageState.equals(Constants.COMMON_STATUS_DEFAULT)), "usageState", usageState)//设备使用状态
                .eq("enabled", Constants.COMMON_STATUS_ENABLED) //状态可用
                //.eq(StringUtils.isNotEmpty(isMySelf) && "1".equals(isMySelf), "creator", currentUser.getUsername()) //只显示自己创建工单
                //.ne(StringUtils.isNotEmpty(isMySelf) && "2".equals(isMySelf), "creator", currentUser.getUsername()) //不显示自己创建的工单

                // 特殊条件
                .eq(!queryMode.equals(Constants.COMMON_STATUS_0) && isAdministratorDepartment.equals(Constants.COMMON_STATUS_0) && isAdministratorCompany.equals(Constants.COMMON_STATUS_0),
                        "principalUsername", currentUser.getUsername())  // 非管理员，只查询自己负责的设备
                .predicate(queryMode.equals(Constants.COMMON_STATUS_1) && StringUtils.isEmpty(responsibleDepartmentCode) && isAdministratorCompany.equals(Constants.COMMON_STATUS_1),// 公司设备管理员
                        Specifications.<EquipmentInfo>or()
                                .eq("responsibleCompanyCode", belongCompanyCode)
                                .eq("responsibleDepartmentCode", belongDepartmentCode)
                                .build())
                .predicate(queryMode.equals(Constants.COMMON_STATUS_1) && StringUtils.isNotEmpty(responsibleDepartmentCode) && isAdministratorCompany.equals(Constants.COMMON_STATUS_1),// 公司设备管理员
                        Specifications.<EquipmentInfo>or()
                                .eq("responsibleCompanyCode", responsibleDepartmentCode)
                                .eq("responsibleDepartmentCode", responsibleDepartmentCode)
                                .build())
                //.predicate(queryMode.equals(Constants.COMMON_STATUS_1) && (isAdministratorDepartment.equals(Constants.COMMON_STATUS_1) && isAdministratorCompany.equals(Constants.COMMON_STATUS_0)),// 部门设备管理员
                //        Specifications.<EquipmentInfo>or()
                //                .eq("responsibleDepartmentCode", belongDepartmentCode)
                //                .build())

                // 特殊条件
                .eq(queryMode.equals(Constants.COMMON_STATUS_0) && StringUtils.isNotEmpty(applicationFormId), "applicationFormId", applicationFormId)//工单id
                .eq(queryMode.equals(Constants.COMMON_STATUS_0) && StringUtils.isEmpty(applicationFormId), "applicationFormId", null)//工单
                .eq(queryMode.equals(Constants.COMMON_STATUS_0) && StringUtils.isEmpty(applicationFormId), "creator", currentUser.getUsername())//创建人
                .build();
        return specification;
    }


    /**
     * 构建设备信息集合
     *
     * @param equipmentDNInfoList 电脑设备集合
     * @param usageType           设备用途
     * @param formId              工单id
     * @return
     */
    private Map<String, Object> buildListByEquipmentDNInfo(List<EquipmentDNInfo> equipmentDNInfoList, String usageType, String formId) {
        // 最终要返回的集合
        Map<String, Object> objectHashMap = new HashMap<>();
        // 最终要返回的设备集合
        List<EquipmentInfo> equipmentInfoList = new ArrayList<>();
        EquipmentInfo equipmentInfo = null;
        // 操作系统
        String osVersionName;
        // OFFICE版本
        String officeVersionName;
        // 设备用途
        String equipmentUsageName;
        // 设备类型
        String equipmentCategoryName;
        // 品牌名称
        String brandName;
        // 责任姓名
        String principalName;
        // 责任人OA账号
        String principalUsername;
        // 资产标签号
        String assetTagNum;
        // 当前登录人
        String currentUserName = SecurityUtils.getCurrentUserName();

        // 是否为有效数字
        Pattern pattern = Pattern.compile("^[0-9]+(.[0-9]+)?$");
        String patternStr = pattern.toString();

        /**
         * 责任部门信息
         */
        Map<String, Object> map = this.findResponsibleOrg();
        String belongCompanyCode = (String) map.get("belongCompanyCode"); // 负责公司
        String belongCompanyName = (String) map.get("belongCompanyName"); // 负责公司
        String belongDepartmentCode = (String) map.get("belongDepartmentCode"); // 负责部门
        String belongDepartmentName = (String) map.get("belongDepartmentName"); // 负责部门
        String responsibleDepartmentDisplayName = (String) map.get("responsibleDisplayName"); // 负责部门
        String isAdministratorDepartment = (String) map.get("isAdministratorDepartment"); // 是否是设备管理员

        // 查询操作系统数据字典值
        List<SysDictValue> osVersionDictValueList = queryDictValueService.queryByType(Constants.DICT_TYPE_OSVERSION);
        // 查询office版本数据字典值
        List<SysDictValue> officeVersionDictValueList = queryDictValueService.queryByType(Constants.DICT_TYPE_OFFICEVERSION);
        // 查询设备类型
        List<EquipmentCategory> categoryList = equipmentCategoryService.findCategoryList(EquipmentCategory.builder().type(Constants.COMMON_STATUS_0).build());
        // 查询设备品牌
        List<EquipmentBrand> brandList = equipmentBrandService.findBrandList(EquipmentBrand.builder().type(Constants.COMMON_STATUS_0).build());
        // 查询未流转下一步，数据库已有的设备列表
        List<EquipmentInfo> infoList = equipmentInfoRepository.queryListByAssetTagNumAndFormId();
        // 如果数据为空
        if (equipmentDNInfoList.isEmpty()) {
            objectHashMap.put("messageContent", "excel数据校验不通过，请检查相关数据!");
            objectHashMap.put("equipmentInfoList", null);
            return objectHashMap;
        }
//        // 去掉第一行表头数据
//        equipmentDNInfoList.remove(0);
        // 存放资产标签号数据
        List<String> tagNumArray = new ArrayList<>();
        // 数据是否满足
        boolean isRight = true;
        // 行数
        int i = 3;
        for (EquipmentDNInfo equipmentDNInfo : equipmentDNInfoList) {
            osVersionName = equipmentDNInfo.getOsVersionName(); // 操作系统
            officeVersionName = equipmentDNInfo.getOfficeVersionName();// OFFICE版本
            equipmentUsageName = equipmentDNInfo.getEquipmentUsageName(); // 设备用途
            equipmentCategoryName = equipmentDNInfo.getEquipmentCategoryName(); // 设备类型
            brandName = equipmentDNInfo.getBrandName(); // 品牌
            principalName = equipmentDNInfo.getPrincipalName();// 责任人姓名
            principalUsername = equipmentDNInfo.getPrincipalUsername();// 责任人OA账号
            assetTagNum = equipmentDNInfo.getAssetTagNum();// 资产标签号
            equipmentInfo = new EquipmentInfo();

            //设备类型，下拉框
            if (StringUtils.isEmpty(equipmentDNInfo.getEquipmentCategoryName())) {
                isRight = false;
                System.out.println(assetTagNum+"**************资产标签号是1111111");
                objectHashMap.put("messageContent", "第" + i + "行[设备类型]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 设备名称
            if (StringUtils.isEmpty(equipmentDNInfo.getEquipmentName())) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[设备名称]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 品牌
            if (StringUtils.isEmpty(equipmentDNInfo.getBrandName())) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[品牌]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 设备用途，下拉框
            if (StringUtils.isEmpty(equipmentDNInfo.getEquipmentUsageName())) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[设备用途]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 规格型号
            if (StringUtils.isEmpty(equipmentDNInfo.getItemModel())) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[规格型号]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 内存
            if (StringUtils.isEmpty(equipmentDNInfo.getMemory())) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[内存]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 硬盘容量
            if (StringUtils.isEmpty(equipmentDNInfo.getDiskCapacity())) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[硬盘容量]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 操作系统
            if (StringUtils.isEmpty(equipmentDNInfo.getOsVersionName())) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[操作系统]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // OFFICE版本
            if (StringUtils.isEmpty(equipmentDNInfo.getOfficeVersionName())) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[OFFICE版本]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            //  责任人姓名
            if (StringUtils.isEmpty(principalName)) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[责任人姓名]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            //  责任人OA账号
            if (StringUtils.isEmpty(principalUsername)) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[责任人OA账号]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 非管理员只能录入自己所负责的设备信息
            if (isAdministratorDepartment.equals(Constants.COMMON_STATUS_0) && !currentUserName.equals(principalUsername)) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[责任人OA账号]列校验不通过，非设备管理员只能录入自己的设备!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }

            /**
             * 校验是责任人是否为有效OA用户
             */
            map.put("principalUsername", principalUsername);
            map.put("principalName", principalName);
            // 校验此责任用户是否符合
            boolean valid = this.isValid(map);
            if (!valid) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[责任人OA账号与责任人]列校验不通过，只允许录入本部门设备信息!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }

            // 校验资产标签号是否有重复存在
            if (StringUtils.isNotEmpty(assetTagNum)) {
                // 本次列表对比
                if (!tagNumArray.isEmpty() && tagNumArray.contains(assetTagNum)) {
                    isRight = false;
                    objectHashMap.put("messageContent", "第" + i + "行[资产标签号]重复存在，请检查相关数据!");
                    objectHashMap.put("equipmentInfoList", null);
                    break;
                }
                // 与数据库已有数据对比
                for (EquipmentInfo info : infoList) {
                    if (info.getAssetTagNum().equals(assetTagNum)) {
                        isRight = false;
                        break;
                    }
                }
                if (!isRight) {
                    objectHashMap.put("messageContent", "第" + i + "行[资产标签号]重复存在，请检查相关数据!");
                    objectHashMap.put("equipmentInfoList", null);
                    break;
                }
                tagNumArray.add(assetTagNum);
            }

            /**
             * 由于excel 表格存储日期的日期字符串，LocalDate无法映射，所以这里先获取日期字符串，再转为LocalDate进行存储
             */
            // 设备生产日期
            String producedDateStr = equipmentDNInfo.getProducedDateStr();
            if (StringUtils.isEmpty(producedDateStr)) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[设备生产日期]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            } else {
                try {
                    if (producedDateStr.contains("/")) {
                        equipmentInfo.setProducedDate(LocalDate.parse(producedDateStr, DateTimeFormatter.ofPattern("yyyy/MM/dd")));
                    }
                    if (producedDateStr.contains("-")) {
                        equipmentInfo.setProducedDate(LocalDate.parse(producedDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    }
                } catch (Exception e) {
                    Exceptions.printException(e);
                    isRight = false;
                    objectHashMap.put("messageContent", "第" + i + "行[设备生产日期]列校验不通过，请检查相关数据!");
                    objectHashMap.put("equipmentInfoList", null);
                }
            }
            // 开始启用日期（参照资产管理系统）
            String startUsingDateStr = equipmentDNInfo.getStartUsingDateStr();
            if (StringUtils.isEmpty(startUsingDateStr)) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[开始启用日期]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            } else {
                try {
                    if (startUsingDateStr.contains("/")) {
                        equipmentInfo.setStartUsingDate(LocalDate.parse(startUsingDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    }
                    if (startUsingDateStr.contains("-")) {
                        equipmentInfo.setStartUsingDate(LocalDate.parse(startUsingDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    }
                } catch (Exception e) {
                    Exceptions.printException(e);
                    isRight = false;
                    objectHashMap.put("messageContent", "第" + i + "行[开始启用日期]列校验不通过，请检查相关数据!");
                    objectHashMap.put("equipmentInfoList", null);
                    break;
                }
            }
            // 对比设备生产日期与开始日期是否满足条件
            LocalDate producedDate = equipmentInfo.getProducedDate();// 设备生产日期
            LocalDate startUsingDate = equipmentInfo.getStartUsingDate();// 设备开始使用日期
            LocalDate now = LocalDate.now();
            // 对比设备生产日期与开始日期是否满足条件，设备生产日期要早于设备开始使用日期
            Period period = Period.between(producedDate, startUsingDate);
            if (period.isZero() || period.getYears() < 0 || period.getMonths() < 0 || period.getDays() < 0) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[设备生产日期与开始使用日期]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            Period period1 = Period.between(producedDate, now);
            if (period1.isZero() || period1.getYears() < 0 || period1.getMonths() < 0 || period1.getDays() < 0) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[设备生产日期]校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            Period period2 = Period.between(startUsingDate, now);
            if (period2.isZero() || period2.getYears() < 0 || period2.getMonths() < 0 || period2.getDays() < 0) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[开始使用日期]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 判断内存、磁盘容量、设备价格是否为有效数字
            // 设备价格
            if (StringUtils.isNotEmpty(equipmentDNInfo.getEquipmentPrice()) && !equipmentDNInfo.getEquipmentPrice().matches(patternStr)) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[设备价格]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 内存
            if (!equipmentDNInfo.getMemory().matches(patternStr)) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[内存]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 磁盘容量
            if (!equipmentDNInfo.getDiskCapacity().matches(patternStr)) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[磁盘容量]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }

            // 免费维保结束日期
            String maintenanceEndDateStr = equipmentDNInfo.getMaintenanceEndDateStr();
            try {
                if (StringUtils.isNotEmpty(maintenanceEndDateStr) && maintenanceEndDateStr.contains("/")) {
                    equipmentInfo.setMaintenanceEndDate(LocalDate.parse(maintenanceEndDateStr, DateTimeFormatter.ofPattern("yyyy/MM/dd")));
                }
                if (StringUtils.isNotEmpty(maintenanceEndDateStr) && maintenanceEndDateStr.contains("-")) {
                    equipmentInfo.setMaintenanceEndDate(LocalDate.parse(maintenanceEndDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                }
            } catch (Exception e) {
                Exceptions.printException(e);
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[免费维保结束日期]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }

            /**
             * 初始数据
             */
            //  如果工单id不为空
            if (StringUtils.isNotEmpty(formId)) {
                equipmentInfo.setApplicationFormId(formId);
            }
            equipmentInfo.setEquipmentType(Constants.COMMON_STATUS_0);// 设备类型→电脑终端设备
            equipmentInfo.setUsageState(Constants.COMMON_STATUS_ENABLED);// 设备使用状态→默认为正常
            //0 导入状态，1 审批中状态，2 生效状态
            equipmentInfo.setStatus(Constants.COMMON_STATUS_DRAFT);// 设备状态→导入时为 0 导入状态

            /**
             * 设备基础信息
             */
            equipmentInfo.setAssetTagNum(equipmentDNInfo.getAssetTagNum());// 资产标签号
            // 如果设备名称为空
            if (StringUtils.isEmpty(equipmentDNInfo.getEquipmentName())) {
                equipmentInfo.setEquipmentName(equipmentCategoryName + "电脑");// 设备名称
            } else {
                equipmentInfo.setEquipmentName(equipmentDNInfo.getEquipmentName());
            }

            /**
             * 设备配置信息
             */
            equipmentInfo.setItemModel(equipmentDNInfo.getItemModel());// 规格型号
            equipmentInfo.setMemory(equipmentDNInfo.getMemory());// 内存
            equipmentInfo.setDiskCapacity(equipmentDNInfo.getDiskCapacity());// 硬盘容量

            /**
             * 归属信息
             */
            equipmentInfo.setPrincipalName(equipmentDNInfo.getPrincipalName());// 责任人姓名
            equipmentInfo.setPrincipalUsername(equipmentDNInfo.getPrincipalUsername());// 责任人OA账号

            /**
             * 设备其他信息
             */
            equipmentInfo.setEquipmentPrice(equipmentDNInfo.getEquipmentPrice());// 设备价格
            equipmentInfo.setEquipmentSite(equipmentDNInfo.getEquipmentSite());// 设备地点名称
            equipmentInfo.setRemark(equipmentDNInfo.getRemark());// 备注

            /**
             * 责任部门相关处理
             */
            equipmentInfo.setResponsibleCompanyCode(belongCompanyCode);
            equipmentInfo.setResponsibleCompanyName(belongCompanyName);
            equipmentInfo.setResponsibleDepartmentCode(belongDepartmentCode);
            equipmentInfo.setResponsibleDepartmentName(belongDepartmentName);
            equipmentInfo.setResponsibleDisplayName(responsibleDepartmentDisplayName);

            // 分类
            for (EquipmentCategory category : categoryList) {
                if (equipmentCategoryName.equalsIgnoreCase(category.getName())) {
                    equipmentInfo.setEquipmentCategoryValue(category.getId());
                    equipmentInfo.setEquipmentCategoryName(category.getName());
                    isRight = true;
                    break;
                } else {
                    isRight = false;
                }
            }
            if (!isRight) {
                System.out.println(assetTagNum+"**************资产标签号是*********");
                objectHashMap.put("messageContent", "第" + i + "行[设备类型]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 品牌
            for (EquipmentBrand brand : brandList) {
                if (equipmentCategoryName.equalsIgnoreCase(brand.getEquipmentCategoryName()) && brandName.equalsIgnoreCase(brand.getName())) {
                    equipmentInfo.setBrandValue(brand.getId());
                    equipmentInfo.setBrandName(brand.getName());
                    isRight = true;
                    break;
                } else {
                    isRight = false;
                }
            }
            if (!isRight) {
                objectHashMap.put("messageContent", "第" + i + "行[品牌]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }

            // 设备用途,这里取分是系统推送，还是手动起草
            // 如果设备用途不为空，则是手动起草
            if (StringUtils.isNotEmpty(usageType)) {
                // 如果设备用途为“营业”
                if (usageType.equals(Constants.COMMON_STATUS_1)) {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_BUSINESS);
                    equipmentInfo.setEquipmentUsageName("营业");
                }
                // 设备用途为“办公”
                else {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_WORK);
                    equipmentInfo.setEquipmentUsageName("办公");
                }
            }
            // 如果设备用途为空，则是系统推送
            else {
                if (equipmentUsageName.contains("办公") || equipmentUsageName.contains("办公用")) {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_WORK);
                    equipmentInfo.setEquipmentUsageName("办公");
                }
                if (equipmentUsageName.contains("营业") || equipmentUsageName.contains("营业用")) {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_BUSINESS);
                    equipmentInfo.setEquipmentUsageName("营业");
                }
            }

            // 操作系统
            for (SysDictValue sysDictValue : osVersionDictValueList) {
                if (osVersionName.equalsIgnoreCase(sysDictValue.getName())) {
                    equipmentInfo.setOsVersionValue(sysDictValue.getValue());
                    equipmentInfo.setOsVersionName(sysDictValue.getName());
                    break;
                }
            }

            // office版本
            for (SysDictValue sysDictValue : officeVersionDictValueList) {
                if (officeVersionName.equalsIgnoreCase(sysDictValue.getName())) {
                    equipmentInfo.setOfficeVersionValue(sysDictValue.getValue());
                    equipmentInfo.setOfficeVersionName(sysDictValue.getName());
                    break;
                }
            }
            equipmentInfoList.add(equipmentInfo);
            i++;
        }
        if (isRight) {
            objectHashMap.put("equipmentInfoList", equipmentInfoList);
        }
        return objectHashMap;
    }


    /**
     * 构建设备信息集合
     */
    private Map<String, Object> buildListByEquipmentWZInfo(List<EquipmentWZInfo> equipmentWZInfoList, String usageType, String formId) {
        // 最终要返回的集合
        Map<String, Object> objectHashMap = new HashMap<>();
        // 最终要返回的设备集合
        List<EquipmentInfo> equipmentInfoList = new ArrayList<>();
        EquipmentInfo equipmentInfo = null;
        // 设备用途
        String equipmentUsageName;
        // 设备类型
        String equipmentCategoryName;
        // 品牌名称
        String brandName;
        // 责任人姓名
        String principalName;
        // 责任人OA账号
        String principalUsername;
        // 资产标签号
        String assetTagNum;
        // 当前登录人
        String currentUserName = SecurityUtils.getCurrentUserName();
        // 是否为有效数字
        Pattern pattern = Pattern.compile("^[0-9]+(.[0-9]+)?$");
        String patternStr = pattern.toString();

        /**
         * 责任部门信息
         */
        Map<String, Object> map = this.findResponsibleOrg();
        String belongCompanyCode = (String) map.get("belongCompanyCode"); // 负责公司
        String belongCompanyName = (String) map.get("belongCompanyName"); // 负责公司
        String belongDepartmentCode = (String) map.get("belongDepartmentCode"); // 负责部门
        String belongDepartmentName = (String) map.get("belongDepartmentName"); // 负责部门
        String responsibleDepartmentDisplayName = (String) map.get("responsibleDisplayName"); // 负责部门
        String isAdministratorDepartment = (String) map.get("isAdministratorDepartment"); // 负责部门

        // 查询设备类型
        List<EquipmentCategory> categoryList = equipmentCategoryService.findCategoryList(EquipmentCategory.builder().type(Constants.COMMON_STATUS_1).build());
        // 查询设备品牌
        List<EquipmentBrand> brandList = equipmentBrandService.findBrandList(EquipmentBrand.builder().type(Constants.COMMON_STATUS_1).build());
        // 查询未流转下一步，数据库已有的设备列表
        List<EquipmentInfo> infoList = equipmentInfoRepository.queryListByAssetTagNumAndFormId();

        // 如果数据为空
        if (equipmentWZInfoList.isEmpty()) {
            objectHashMap.put("messageContent", "excel数据校验不通过，请检查相关数据!");
            objectHashMap.put("equipmentInfoList", null);
            return map;
        }
//        // 去掉第一行表头数据
//        equipmentWZInfoList.remove(0);
        // 存放资产标签号数据
        List<String> tagNumArray = new ArrayList<>();
        // 数据是否满足
        boolean isRight = true;
        // 行数
        int i = 3;
        for (EquipmentWZInfo equipmentWZInfo : equipmentWZInfoList) {
            equipmentUsageName = equipmentWZInfo.getEquipmentUsageName(); // 设备用途
            equipmentCategoryName = equipmentWZInfo.getEquipmentCategoryName(); // 设备类型
            brandName = equipmentWZInfo.getBrandName(); // 品牌
            principalName = equipmentWZInfo.getPrincipalName(); // 责任人姓名
            principalUsername = equipmentWZInfo.getPrincipalUsername();// 责任人OA账号
            assetTagNum = equipmentWZInfo.getAssetTagNum();// 资产标签号
            equipmentInfo = new EquipmentInfo();

            //设备类型，下拉框
            if (StringUtils.isEmpty(equipmentCategoryName)) {
                isRight = false;
                System.out.println(assetTagNum+"**************资产标签号是3333333333");
                objectHashMap.put("messageContent", "第" + i + "行[设备类型]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 设备名称
            if (StringUtils.isEmpty(equipmentWZInfo.getEquipmentName())) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[设备名称]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 品牌
            if (StringUtils.isEmpty(brandName)) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[品牌]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 设备用途，下拉框
            if (StringUtils.isEmpty(equipmentUsageName)) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[设备用途]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 规格型号
            if (StringUtils.isEmpty(equipmentWZInfo.getItemModel())) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[规格型号]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            //  责任人姓名
            if (StringUtils.isEmpty(equipmentWZInfo.getPrincipalName())) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[责任人姓名]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            //  责任人OA账号
            if (StringUtils.isEmpty(equipmentWZInfo.getPrincipalUsername())) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[责任人OA账号]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 非管理员只能录入自己所负责的设备信息
            if (isAdministratorDepartment.equals(Constants.COMMON_STATUS_0) && !currentUserName.equals(principalUsername)) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[责任人OA账号]列校验不通过，非设备管理员只能录入自己的设备!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            /**
             * 校验是责任人是否为有效OA用户
             */
            map.put("principalUsername", principalUsername);
            map.put("principalName", principalName);
            // 校验此责任用户是否符合
            boolean valid = this.isValid(map);
            if (!valid) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[责任人OA账号与责任人]列校验不通过，只允许录入本部门设备信息!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }

            // 校验资产标签号是否有重复存在
            if (StringUtils.isNotEmpty(assetTagNum)) {
                // 本次列表对比
                if (!tagNumArray.isEmpty() && tagNumArray.contains(assetTagNum)) {
                    isRight = false;
                    objectHashMap.put("messageContent", "第" + i + "行[资产标签号]重复存在，请检查相关数据!");
                    objectHashMap.put("equipmentInfoList", null);
                    break;
                }
                // 与数据库已有数据对比
                for (EquipmentInfo info : infoList) {
                    if (info.getAssetTagNum().equals(assetTagNum)) {
                        isRight = false;
                        break;
                    }
                }
                if (!isRight) {
                    objectHashMap.put("messageContent", "第" + i + "行[资产标签号]重复存在，请检查相关数据!");
                    objectHashMap.put("equipmentInfoList", null);
                    break;
                }
                tagNumArray.add(assetTagNum);
            }

            /**
             * 由于excel 表格存储日期的日期字符串，LocalDate无法映射，所以这里先获取日期字符串，再转为LocalDate进行存储
             */
            // 设备生产日期
            String producedDateStr = equipmentWZInfo.getProducedDateStr();
            if (StringUtils.isEmpty(producedDateStr)) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[设备生产日期]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            } else {
                try {
                    if (producedDateStr.contains("/")) {
                        equipmentInfo.setProducedDate(LocalDate.parse(producedDateStr, DateTimeFormatter.ofPattern("yyyy/MM/dd")));
                    }
                    if (producedDateStr.contains("-")) {
                        equipmentInfo.setProducedDate(LocalDate.parse(producedDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    }
                } catch (Exception e) {
                    Exceptions.printException(e);
                    isRight = false;
                    objectHashMap.put("messageContent", "第" + i + "行[设备生产日期]列校验不通过，请检查相关数据!");
                    objectHashMap.put("equipmentInfoList", null);
                    break;
                }
            }
            // 开始启用日期（参照资产管理系统）
            String startUsingDateStr = equipmentWZInfo.getStartUsingDateStr();
            if (StringUtils.isEmpty(startUsingDateStr)) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[开始启用日期]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            } else {
                try {
                    if (startUsingDateStr.contains("/")) {
                        equipmentInfo.setStartUsingDate(LocalDate.parse(startUsingDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    }
                    if (startUsingDateStr.contains("-")) {
                        equipmentInfo.setStartUsingDate(LocalDate.parse(startUsingDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    }
                } catch (Exception e) {
                    Exceptions.printException(e);
                    isRight = false;
                    objectHashMap.put("messageContent", "第" + i + "行[开始启用日期]列校验不通过，请检查相关数据!");
                    objectHashMap.put("equipmentInfoList", null);
                    break;
                }
            }
            // 对比设备生产日期与开始日期是否满足条件
            LocalDate producedDate = equipmentInfo.getProducedDate();// 设备生产日期
            LocalDate startUsingDate = equipmentInfo.getStartUsingDate();// 设备开始使用日期
            LocalDate now = LocalDate.now();
            // 对比设备生产日期与开始日期是否满足条件，设备生产日期要早于设备开始使用日期
            Period period = Period.between(producedDate, startUsingDate);
            if (period.isZero() || period.getYears() < 0 || period.getMonths() < 0 || period.getDays() < 0) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[设备生产日期与开始使用日期]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            Period period1 = Period.between(producedDate, now);
            if (period1.isZero() || period1.getYears() < 0 || period1.getMonths() < 0 || period1.getDays() < 0) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[设备生产日期]校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            Period period2 = Period.between(startUsingDate, now);
            if (period2.isZero() || period2.getYears() < 0 || period2.getMonths() < 0 || period2.getDays() < 0) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[开始使用日期]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 判断内存、磁盘容量、设备价格是否为有效数字
            // 设备价格
            if (StringUtils.isNotEmpty(equipmentWZInfo.getEquipmentPrice()) && !equipmentWZInfo.getEquipmentPrice().matches(patternStr)) {
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[设备价格]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }

            // 免费维保结束日期
            String maintenanceEndDateStr = equipmentWZInfo.getMaintenanceEndDateStr();
            try {
                if (StringUtils.isNotEmpty(maintenanceEndDateStr) && maintenanceEndDateStr.contains("/")) {
                    equipmentInfo.setMaintenanceEndDate(LocalDate.parse(maintenanceEndDateStr, DateTimeFormatter.ofPattern("yyyy/MM/dd")));
                }
                if (StringUtils.isNotEmpty(maintenanceEndDateStr) && maintenanceEndDateStr.contains("-")) {
                    equipmentInfo.setMaintenanceEndDate(LocalDate.parse(maintenanceEndDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                }
            } catch (Exception e) {
                Exceptions.printException(e);
                isRight = false;
                objectHashMap.put("messageContent", "第" + i + "行[免费维保结束日期]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                e.printStackTrace();
            }

            /**
             * 初始数据
             */
            //  如果工单id不为空
            if (StringUtils.isNotEmpty(formId)) {
                equipmentInfo.setApplicationFormId(formId);
            }
            equipmentInfo.setEquipmentType(Constants.COMMON_STATUS_1);// 设备类型→外置设备
            equipmentInfo.setUsageState(Constants.COMMON_STATUS_ENABLED);// 设备使用状态→默认为正常
            //0 导入状态，1 审批中状态，2 生效状态
            equipmentInfo.setStatus(Constants.COMMON_STATUS_DRAFT);// 设备状态→导入时为 0 导入状态

            /**
             * 设备基础信息
             */
            equipmentInfo.setAssetTagNum(equipmentWZInfo.getAssetTagNum());// 资产标签号
            equipmentInfo.setEquipmentName(equipmentWZInfo.getEquipmentName());

            /**
             * 设备配置信息
             */
            equipmentInfo.setItemModel(equipmentWZInfo.getItemModel());// 规格型号

            /**
             * 归属信息
             */
            equipmentInfo.setPrincipalName(equipmentWZInfo.getPrincipalName());// 责任人姓名
            equipmentInfo.setPrincipalUsername(equipmentWZInfo.getPrincipalUsername());// 责任人OA账号

            /**
             * 设备其他信息
             */
            equipmentInfo.setEquipmentPrice(equipmentWZInfo.getEquipmentPrice());// 设备价格
            equipmentInfo.setEquipmentSite(equipmentWZInfo.getEquipmentSite());// 设备地点名称
            equipmentInfo.setRemark(equipmentWZInfo.getRemark());// 备注

            /**
             * 责任部门相关处理
             */
            equipmentInfo.setResponsibleCompanyCode(belongCompanyCode);
            equipmentInfo.setResponsibleCompanyName(belongCompanyName);
            equipmentInfo.setResponsibleDepartmentCode(belongDepartmentCode);
            equipmentInfo.setResponsibleDepartmentName(belongDepartmentName);
            equipmentInfo.setResponsibleDisplayName(responsibleDepartmentDisplayName);

            // 分类
            for (EquipmentCategory category : categoryList) {
                if (equipmentCategoryName.equalsIgnoreCase(category.getName())) {
                    equipmentInfo.setEquipmentCategoryValue(category.getId());
                    equipmentInfo.setEquipmentCategoryName(category.getName());
                    isRight = true;
                    break;
                } else {
                    isRight = false;
                }
            }
            if (!isRight) {
                System.out.println(assetTagNum+"**************资产标签号是444444444444");
                objectHashMap.put("messageContent", "第" + i + "行[设备类型]列校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }
            // 品牌
            for (EquipmentBrand brand : brandList) {
                if (equipmentCategoryName.equalsIgnoreCase(brand.getEquipmentCategoryName()) && brandName.equalsIgnoreCase(brand.getName())) {
                    equipmentInfo.setBrandValue(brand.getId());
                    equipmentInfo.setBrandName(brand.getName());
                    isRight = true;
                    break;
                } else {
                    isRight = false;
                }
            }
            if (!isRight) {
                objectHashMap.put("messageContent", "第" + i + "行[品牌]列校校验不通过，请检查相关数据!");
                objectHashMap.put("equipmentInfoList", null);
                break;
            }

            // 设备用途,这里取分是系统推送，还是手动起草
            // 如果设备用途不为空，则是手动起草
            if (StringUtils.isNotEmpty(usageType)) {
                // 如果设备用途为“营业”
                if (usageType.equals(Constants.COMMON_STATUS_1)) {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_BUSINESS);
                    equipmentInfo.setEquipmentUsageName("营业");
                }
                // 设备用途为“办公”
                else {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_WORK);
                    equipmentInfo.setEquipmentUsageName("办公");
                }
            }
            // 如果设备用途为空，则是系统推送
            else {
                if (equipmentUsageName.contains("办公") || equipmentUsageName.contains("办公用")) {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_WORK);
                    equipmentInfo.setEquipmentUsageName("办公");
                }
                if (equipmentUsageName.contains("营业") || equipmentUsageName.contains("营业用")) {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_BUSINESS);
                    equipmentInfo.setEquipmentUsageName("营业");
                }
            }
            equipmentInfoList.add(equipmentInfo);
            i++;
        }
        if (isRight) {
            objectHashMap.put("equipmentInfoList", equipmentInfoList);
        }
        return objectHashMap;
    }

    /**
     * 校验是责任人是否为有效OA用户
     */
    private boolean isValid(Map<String, Object> map) {
        boolean isRight = false;
        String belongCompanyCode = (String) map.get("belongCompanyCode"); // 设备管理员所属公司
        String belongDepartmentCode = (String) map.get("belongDepartmentCode"); // 设备管理员所属部门
        String principalName = (String) map.get("principalName"); // 责任人姓名
        String principalUsername = (String) map.get("principalUsername"); // 责任人OA账号
        String principalDepartmentCode = ""; // 责任人所属部门
        String principalCompanyCode = "";// 责任人所属公司
        // 当前登录人
        IUser currentUser = SecurityUtils.getCurrentUser();
        try {
            //uumsSysUserinfoApi.findByKey(principalUsername, IAuthService.KeyType.username, Constants.APP_CODE);
            log.debug("---设备负责人----" + principalUsername);
            SimpleUser simpleUser = uumsSysUserinfoApi.findByUsernameFromCurrent(principalUsername, Constants.APP_CODE);
            if (simpleUser != null) {
                // 1、判断责任人姓名与责任人OA账号是否一致
                if (!simpleUser.getTruename().equals(principalName)) {
                    return false;
                }
                // 2、判断责任人是否属于当前设备管理员所管理
                // 判断责任人所属公司级别
                //if (simpleUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_COUNTY)) {
                //    // 获取当前登录人所属公司code,
                //    principalDepartmentCode = simpleUser.getBelongCompanyCode();
                //    SimpleOrg parentBySon = uumsSysOrgApi.findParentBySon(Constants.APP_CODE, belongDepartmentCode);
                //    principalCompanyCode = parentBySon.getOrgCode();
                //}
                //// 如果当前登录人所属公司为省公司、分公司
                //else {
                //    principalCompanyCode = simpleUser.getBelongCompanyCode();
                //    principalDepartmentCode = simpleUser.getBelongDepartmentCode();
                //}
                //if (!principalCompanyCode.equals(belongCompanyCode) || !(principalDepartmentCode.equals(belongDepartmentCode) || belongDepartmentCode.equals(simpleUser.getBelongOrgCode()))) {
                //    return false;
                //}
                /**
                 * 新的校验规则，判断待操作人是否所属责任人所在组织
                 */
                if (currentUser.getBelongCompanyTypeDictValue().equals(Constants.COMPANY_TYPE_PROVINCE)) {
                    principalCompanyCode = simpleUser.getBelongCompanyCode();
                    principalDepartmentCode = simpleUser.getBelongDepartmentCode();
                    if (principalCompanyCode.equals(belongCompanyCode) && (belongDepartmentCode.equals(principalDepartmentCode) || belongDepartmentCode.equals(simpleUser.getBelongOrgCode()))) {
                        isRight = true;
                    }
                } else {
                    // 责任人组织信息
                    Set<SimpleOrg> principalAuthOrgs = simpleUser.getAuthOrgs();
                    // 当前登录人组织信息
                    Set<SimpleOrg> currentUserAuthOrgs = (Set<SimpleOrg>) currentUser.getAuthOrgs();
                    for (SimpleOrg principalAuthOrg : principalAuthOrgs) {
                        for (SimpleOrg currentUserAuthOrg : currentUserAuthOrgs) {
                            if (principalAuthOrg.getDisplayName().contains(currentUserAuthOrg.getDisplayName()) ||
                                    currentUserAuthOrg.getDisplayName().contains(principalAuthOrg.getDisplayName()) ||
                                    principalAuthOrg.getBelongDepartmentCode().equals(currentUserAuthOrg.getBelongDepartmentCode()) ||
                                    principalAuthOrg.getBelongCompanyCode().equals(currentUserAuthOrg.getBelongCompanyCode())) {
                                isRight = true;
                                break;
                            }
                        }
                        if (isRight) break;
                    }
                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return isRight;
    }















    /**
     * 查询设备列表
     * 维护功能使用
     *
     * @param page          当前页码
     * @param rows          每页数量
     * @param direction     排序规则（asc/desc）
     * @param properties    排序规则（属性名称）
     * @param equipmentInfo 设备信息
     * @return
     */
    @Override
    public JsonResponse findByAllEqument(int page, int rows, String direction, String properties, EquipmentInfo equipmentInfo) {
        // 排序规则
        if (StringUtils.isEmpty(direction)) {
            direction = "desc";
        }
        if (StringUtils.isEmpty(properties)) {
            properties = "createdTime";
        }
        // 分页对象
        Pageable pageable = this.getPageable(page, rows, direction, properties);
        // 构建查询条件
        Specification<EquipmentInfo> specification = this.buildSpecEqument(equipmentInfo);
        // 获取查询结果
        Page<EquipmentInfo> pages = this.findAll(specification, pageable);

        return JsonResponse.success(pages);
    }

    /**
     * 新增设备信息
     * 维护功能使用
     *
     * @param form
     * @return
     */
    @Override
    public JsonResponse createEquipment(EquipmentInfo form) {
        // 资产标签号
        String assetTagNum = form.getAssetTagNum();
        boolean isExistTagNum = false;
        if (StringUtils.isNotEmpty(assetTagNum)) {
            // 查询未流转下一步，数据库已有的设备列表
            List<EquipmentInfo> infoList = equipmentInfoRepository.queryListByAssetTagNumAndFormId();
            for (EquipmentInfo info : infoList) {
                if (assetTagNum.equalsIgnoreCase(info.getAssetTagNum())) {
                    isExistTagNum = true;
                    break;
                }
            }
        }
        // 新增时，默认设备状态
        form.setUsageState(Constants.COMMON_STATUS_ENABLED);
        // 新增时，默认设备录入状态 0 导入状态，1 审批中状态，2 生效状态
        form.setStatus(Constants.COMMON_STATUS_EFFICIENT);
        // 如果设备用途为空，则先查询设备用途数据字典
        if (StringUtils.isEmpty(form.getEquipmentUsageName())) {
            List<SysDictValue> sysDictValueList = queryDictValueService.queryByType(Constants.DICT_TYPE_EQUIPMENTUSAGE);
            for (SysDictValue sysDictValue : sysDictValueList) {
                if (sysDictValue.getValue().equals(form.getEquipmentUsageValue())) {
                    form.setEquipmentUsageName(sysDictValue.getName());
                    break;
                }
            }
        }
        form.setId(null);
        EquipmentInfo equipmentInfo =insert(form);
        return JsonResponse.success("数据更新成功");
    }

    /**
     * 更新设备信息
     *
     * @param form
     * @return
     */
    @Override
    public JsonResponse updateEquipment(EquipmentInfo form) {
        if (StringUtils.isNotEmpty(form.getAssetTagNum())) {
            Specification<EquipmentInfo> specification = Specifications.<EquipmentInfo>and()
                    .ne(StringUtils.isNotEmpty(form.getId()), "id", form.getId())//ID
                    .eq("assetTagNum", form.getAssetTagNum())
                    .eq("enabled", Constants.COMMON_STATUS_ENABLED) //状态可用
                    .build();
            Iterable<EquipmentInfo> iterable = this.findAllNoPage(specification);
        }
        equipmentInfoService.update(form);
        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 导入设备信息
     * 维护功能使用
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @Override
    public void importExcelWeihu(HttpServletRequest request, HttpServletResponse response) {
        JsonResponse jsonResponse = JsonResponse.defaultSuccessResponse();
        try {
            response.setContentType("text/html; charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            PrintWriter out = response.getWriter();
            MultipartHttpServletRequest mureq = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> multipartFiles = mureq.getFileMap();
            // 附件excel 文件名
            String originalFilename = "";
            for (MultipartFile uploadfile : multipartFiles.values()) {
                /**
                 * 这里要区分是“外置设备”还是“电脑终端设备”,处理之后返回最终的设备信息集合
                 */
                originalFilename = uploadfile.getOriginalFilename();
                // 设备类型
                String equipmentType = request.getParameter("equipmentType");
                // 设备用途
                String usageType = request.getParameter("usageType");
                // 工单id
                String formId = request.getParameter("formId");

                List<EquipmentInfo> equipmentInfoList;
                // 如果为电脑终端设备
                if (StringUtils.isNotEmpty(equipmentType) && equipmentType.contains(Constants.COMMON_STATUS_0)) {
                    if (StringUtils.isNotEmpty(originalFilename) && originalFilename.contains("电脑终端")) {
                        // 先上传至sys_file表,注意sheetName名要与excel保持一致
                        UploadFileResponse uploadFileResponse = fileService.importExcel(uploadfile, request.getParameter("pmInsType"), request.getParameter("pmInsId"), request.getParameter("pmInsTypePart"), EquipmentDNInfo.class, "设备信息", 2);
                        // 获取excel表格
                        List<EquipmentDNInfo> equipmentDNInfoList = uploadFileResponse.getListData();
                        // 构建设备信息集合
                        Map<String, Object> map = this.buildListByEquipmentDNInfoWeihu(equipmentDNInfoList, usageType, formId);
                        equipmentInfoList = (List<EquipmentInfo>) map.get("equipmentInfoList");
                        if (equipmentInfoList == null) {
                            jsonResponse.setMessage(map.get("messageContent").toString());
                        }
                        uploadFileResponse.setListData(equipmentInfoList);
                        jsonResponse.setData(uploadFileResponse);
                    } else {
                        jsonResponse.setMessage("请确认上传的附件是否正确!");
                        jsonResponse.setData(null);
                    }
                }
                // 如果为外置设备
                if (StringUtils.isNotEmpty(equipmentType) && equipmentType.contains(Constants.COMMON_STATUS_1)) {
                    if (StringUtils.isNotEmpty(originalFilename) && originalFilename.contains("外置设备")) {
                        // 先上传至sys_file表,注意sheetName名要与excel保持一致
                        UploadFileResponse uploadFileResponse = fileService.importExcel(uploadfile, request.getParameter("pmInsType"), request.getParameter("pmInsId"), request.getParameter("pmInsTypePart"), EquipmentWZInfo.class, "设备信息", 2);
                        // 获取excel表格
                        List<EquipmentWZInfo> equipmentWZInfoList = uploadFileResponse.getListData();
                        // 构建设备信息集合
                        Map<String, Object> map = this.buildListByEquipmentWZInfoWeihu(equipmentWZInfoList, usageType, formId);
                        equipmentInfoList = (List<EquipmentInfo>) map.get("equipmentInfoList");
                        if (equipmentInfoList == null) {
                            jsonResponse.setMessage(map.get("messageContent").toString());
                        }
                        uploadFileResponse.setListData(equipmentInfoList);
                        jsonResponse.setData(uploadFileResponse);
                    } else {
                        jsonResponse.setMessage("请确认上传的附件是否正确!");
                        jsonResponse.setData(null);
                    }
                }
            }
            String result = "<script type=\"text/javascript\">parent.result=" + JacksonUtils.obj2json(jsonResponse) + "</script>";
            out.println(result);
            out.close();
        } catch (Exception e) {
            jsonResponse.setErrcode(-1);
            Exceptions.printException(e);
        }
    }

    /**
     * 保存导入的设备信息
     *
     * @param equipmentInfo
     * @return
     * @throws Exception
     */
    @Override
    public JsonResponse saveEquipmentInfoListWeihu(EquipmentInfo equipmentInfo) {
        // 获取设备信息集合
        List<EquipmentInfo> equipmentInfoList = equipmentInfo.getEquipmentInfoList();
        try {
            if (equipmentInfoList == null || equipmentInfoList.isEmpty()) {
                return JsonResponse.fail("数据为空，请重新上传文件!");
            }
            // 查询未流转下一步，数据库已有的设备列表
            List<EquipmentInfo> infoList = equipmentInfoRepository.queryListByAssetTagNumAndFormId();
            // 是否存在未流转时，资产标签号重复情况
            boolean isExistTagNum = false;
            // 校验是否有未流转，但资产标签号已存在的情况
            for (EquipmentInfo equipmentInfo1 : equipmentInfoList) {
                if (StringUtils.isNotEmpty(equipmentInfo1.getAssetTagNum())) {
                    for (EquipmentInfo info : infoList) {
                        if (equipmentInfo1.getAssetTagNum().equalsIgnoreCase(info.getAssetTagNum())) {
                            isExistTagNum = true;
                            break;
                        }
                    }
                }
                if (isExistTagNum) break;
            }
            if (isExistTagNum) {
                return JsonResponse.fail("资产标签号不能重复，请检查相关数据!");
            }
            this.saveAll(equipmentInfoList);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return JsonResponse.success(equipmentInfoList);
    }


    /**
     * 构建查询条件
     *
     * @param form
     * @return
     */
    private Specification<EquipmentInfo> buildSpecEqument(EquipmentInfo form) {
        // 查询方式,0:设备录入查询，1:设备生效之后的列表查询
        String queryMode = form.getQueryMode();
        // 资产标签号
        String assetTagNum = form.getAssetTagNum();
        // 设备编号
        String uniqueNum = form.getUniqueNum();
        // 责任人姓名
        String principalName = form.getPrincipalName();
        // 责任人OA账号
        String principalUsername = form.getPrincipalUsername();
        // 设备类型
        String equipmentType = form.getEquipmentType();
        // 设备录入状态
        Integer status = form.getStatus();
        // 设备使用状态
        Integer usageState = form.getUsageState();
        // 设备类型
        String equipmentCategoryValue = form.getEquipmentCategoryValue();
        // 设备名称
        String equipmentName = form.getEquipmentName();
        // 设备关联表单id
        String applicationFormId = form.getApplicationFormId();
        // 设备登记人
        String creator = form.getCreator();
        // 设备地点
        String equipmentSite = form.getEquipmentSite();
        // 设备用途
        String equipmentUsageValue = form.getEquipmentUsageValue();
        // 负责部门
        String responsibleDepartmentCode = form.getResponsibleDepartmentCode();
        // 当前登录人
        IUser currentUser = SecurityUtils.getCurrentUser();
        // 设备登记日期
        String createdDateTime = form.getCreatedDateTime();

        // 当查询方式为“设备生效”列表搜索,处理相关查询条件
        String belongCompanyCode = "";
        String belongDepartmentCode = "";
        String isAdministratorDepartment = "";
        String isAdministratorCompany = "";
        // 处理设备登记日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = null;
        if (StringUtils.isNotEmpty(createdDateTime)) {
            dateTime = LocalDateTime.parse(createdDateTime + " 00:00:00", formatter);
        }
        /**
         * 构建查询条件
         */
        Specification<EquipmentInfo> specification = Specifications.<EquipmentInfo>and()
                .like(StringUtils.isNotEmpty(assetTagNum), "assetTagNum", "%" + assetTagNum + "%")//资产标签号
                .like(StringUtils.isNotEmpty(uniqueNum), "uniqueNum", "%" + uniqueNum + "%")//设备编号
                .like(StringUtils.isNotEmpty(equipmentName), "equipmentName", "%" + equipmentName + "%")//设备名称
                .like(StringUtils.isNotEmpty(principalName), "principalName", "%" + principalName + "%")//责任人姓名
                .like(StringUtils.isNotEmpty(principalUsername), "principalUsername", "%" + principalUsername + "%")//责任人OA账号
                .eq(StringUtils.isNotEmpty(equipmentType), "equipmentType", equipmentType)//设备类型
                .eq(StringUtils.isNotEmpty(equipmentCategoryValue), "equipmentCategoryValue", equipmentCategoryValue)//设备类型
                .eq(StringUtils.isNotEmpty(equipmentUsageValue), "equipmentUsageValue", equipmentUsageValue)//设备使用状态
                .eq(StringUtils.isNotEmpty(equipmentSite), "equipmentSite", equipmentSite)//设备地点
                .eq(StringUtils.isNotEmpty(creator), "creator", creator)//登记人OA账号
                .gt(dateTime != null, "createdTime", dateTime)//登记日期
                .eq(StringUtils.isNotEmpty(responsibleDepartmentCode) && (isAdministratorDepartment.equals(Constants.COMMON_STATUS_1)), "responsibleDepartmentCode", responsibleDepartmentCode)//责任部门
                .eq((null != status), "status", status)//设备录入状态
                .eq((null != usageState && !usageState.equals(Constants.COMMON_STATUS_DEFAULT)), "usageState", usageState)//设备使用状态
                .eq("enabled", Constants.COMMON_STATUS_ENABLED) //状态可用
                // 特殊条件
                .eq(queryMode.equals(Constants.COMMON_STATUS_0) && StringUtils.isNotEmpty(applicationFormId), "applicationFormId", applicationFormId)//工单id
                .eq(queryMode.equals(Constants.COMMON_STATUS_0) && StringUtils.isEmpty(applicationFormId), "applicationFormId", null)//工单
                .eq(queryMode.equals(Constants.COMMON_STATUS_0) && StringUtils.isEmpty(applicationFormId), "creator", currentUser.getUsername())//创建人
                .build();
        return specification;
    }




    /**
     * 构建设备信息集合
     *
     * @param equipmentDNInfoList 电脑设备集合
     * @param usageType           设备用途
     * @param formId              工单id
     * @return
     */
    private Map<String, Object> buildListByEquipmentDNInfoWeihu(List<EquipmentDNInfo> equipmentDNInfoList, String usageType, String formId) {
        // 最终要返回的集合
        Map<String, Object> objectHashMap = new HashMap<>();
        // 最终要返回的设备集合
        List<EquipmentInfo> equipmentInfoList = new ArrayList<>();
        EquipmentInfo equipmentInfo = null;
        // 操作系统
        String osVersionName;
        // OFFICE版本
        String officeVersionName;
        // 设备用途
        String equipmentUsageName;
        // 设备类型
        String equipmentCategoryName;
        // 品牌名称
        String brandName;
        // 责任姓名
        String principalName;
        // 责任人OA账号
        String principalUsername;
        // 资产标签号
        String assetTagNum;
        // 当前登录人
        String currentUserName = SecurityUtils.getCurrentUserName();

        // 是否为有效数字
        Pattern pattern = Pattern.compile("^[0-9]+(.[0-9]+)?$");
        String patternStr = pattern.toString();

        /**
         * 责任部门信息
         */
        Map<String, Object> map = this.findResponsibleOrg();
        String belongCompanyCode = (String) map.get("belongCompanyCode"); // 负责公司
        String belongCompanyName = (String) map.get("belongCompanyName"); // 负责公司
        String belongDepartmentCode = (String) map.get("belongDepartmentCode"); // 负责部门
        String belongDepartmentName = (String) map.get("belongDepartmentName"); // 负责部门
        String responsibleDepartmentDisplayName = (String) map.get("responsibleDisplayName"); // 负责部门
        String isAdministratorDepartment = (String) map.get("isAdministratorDepartment"); // 是否是设备管理员

        // 查询操作系统数据字典值
        List<SysDictValue> osVersionDictValueList = queryDictValueService.queryByType(Constants.DICT_TYPE_OSVERSION);
        // 查询office版本数据字典值
        List<SysDictValue> officeVersionDictValueList = queryDictValueService.queryByType(Constants.DICT_TYPE_OFFICEVERSION);
        // 查询设备类型
        List<EquipmentCategory> categoryList = equipmentCategoryService.findCategoryList(EquipmentCategory.builder().type(Constants.COMMON_STATUS_0).build());
        // 查询设备品牌
        List<EquipmentBrand> brandList = equipmentBrandService.findBrandList(EquipmentBrand.builder().type(Constants.COMMON_STATUS_0).build());
        // 查询未流转下一步，数据库已有的设备列表
        List<EquipmentInfo> infoList = equipmentInfoRepository.queryListByAssetTagNumAndFormId();
        // 如果数据为空
        if (equipmentDNInfoList.isEmpty()) {
            objectHashMap.put("messageContent", "excel数据校验不通过，请检查相关数据!");
            objectHashMap.put("equipmentInfoList", null);
            return objectHashMap;
        }
//        // 去掉第一行表头数据
//        equipmentDNInfoList.remove(0);
        // 存放资产标签号数据
        List<String> tagNumArray = new ArrayList<>();
        // 数据是否满足
        boolean isRight = true;
        // 行数
        int i = 3;
        for (EquipmentDNInfo equipmentDNInfo : equipmentDNInfoList) {
            osVersionName = equipmentDNInfo.getOsVersionName(); // 操作系统
            officeVersionName = equipmentDNInfo.getOfficeVersionName();// OFFICE版本
            equipmentUsageName = equipmentDNInfo.getEquipmentUsageName(); // 设备用途
            equipmentCategoryName = equipmentDNInfo.getEquipmentCategoryName(); // 设备类型
            brandName = equipmentDNInfo.getBrandName(); // 品牌
            principalName = equipmentDNInfo.getPrincipalName();// 责任人姓名
            principalUsername = equipmentDNInfo.getPrincipalUsername();// 责任人OA账号
            assetTagNum = equipmentDNInfo.getAssetTagNum();// 资产标签号
            equipmentInfo = new EquipmentInfo();
            // 校验资产标签号是否有重复存在
            if (StringUtils.isNotEmpty(assetTagNum)) {
                // 本次列表对比
                if (!tagNumArray.isEmpty() && tagNumArray.contains(assetTagNum)) {
                    isRight = false;
                    objectHashMap.put("messageContent", "第" + i + "行[资产标签号]重复存在，请检查相关数据!");
                    objectHashMap.put("equipmentInfoList", null);
                    break;
                }
                // 与数据库已有数据对比
                for (EquipmentInfo info : infoList) {
                    if (info.getAssetTagNum().equals(assetTagNum)) {
                        isRight = false;
                        break;
                    }
                }
                if (!isRight) {
                    objectHashMap.put("messageContent", "第" + i + "行[资产标签号]重复存在，请检查相关数据!");
                    objectHashMap.put("equipmentInfoList", null);
                    break;
                }
                tagNumArray.add(assetTagNum);
            }

            /**
             * 初始数据
             */
            //  如果工单id不为空
            if (StringUtils.isNotEmpty(formId)) {
                equipmentInfo.setApplicationFormId(formId);
            }
            equipmentInfo.setEquipmentType(Constants.COMMON_STATUS_0);// 设备类型→电脑终端设备
            equipmentInfo.setUsageState(Constants.COMMON_STATUS_ENABLED);// 设备使用状态→默认为正常
            //0 导入状态，1 审批中状态，2 生效状态
            equipmentInfo.setStatus(Constants.COMMON_STATUS_DRAFT);// 设备状态→导入时为 0 导入状态

            /**
             * 设备基础信息
             */
            equipmentInfo.setAssetTagNum(equipmentDNInfo.getAssetTagNum());// 资产标签号
            // 如果设备名称为空
            if (StringUtils.isEmpty(equipmentDNInfo.getEquipmentName())) {
                equipmentInfo.setEquipmentName(equipmentCategoryName + "电脑");// 设备名称
            } else {
                equipmentInfo.setEquipmentName(equipmentDNInfo.getEquipmentName());
            }

            /**
             * 设备配置信息
             */
            equipmentInfo.setItemModel(equipmentDNInfo.getItemModel());// 规格型号
            equipmentInfo.setMemory(equipmentDNInfo.getMemory());// 内存
            equipmentInfo.setDiskCapacity(equipmentDNInfo.getDiskCapacity());// 硬盘容量

            /**
             * 归属信息
             */
            equipmentInfo.setPrincipalName(equipmentDNInfo.getPrincipalName());// 责任人姓名
            equipmentInfo.setPrincipalUsername(equipmentDNInfo.getPrincipalUsername());// 责任人OA账号

            /**
             * 设备其他信息
             */
            equipmentInfo.setEquipmentPrice(equipmentDNInfo.getEquipmentPrice());// 设备价格
            equipmentInfo.setEquipmentSite(equipmentDNInfo.getEquipmentSite());// 设备地点名称
            equipmentInfo.setRemark(equipmentDNInfo.getRemark());// 备注

            /**
             * 责任部门相关处理
             */
            equipmentInfo.setResponsibleCompanyCode(belongCompanyCode);
            equipmentInfo.setResponsibleCompanyName(belongCompanyName);
            equipmentInfo.setResponsibleDepartmentCode(belongDepartmentCode);
            equipmentInfo.setResponsibleDepartmentName(belongDepartmentName);
            equipmentInfo.setResponsibleDisplayName(responsibleDepartmentDisplayName);

            // 分类
            for (EquipmentCategory category : categoryList) {
                if (equipmentCategoryName.equalsIgnoreCase(category.getName())) {
                    equipmentInfo.setEquipmentCategoryValue(category.getId());
                    equipmentInfo.setEquipmentCategoryName(category.getName());
                    isRight = true;
                    break;
                } else {
                    isRight = false;
                }
            }

            // 品牌
            for (EquipmentBrand brand : brandList) {
                if (equipmentCategoryName.equalsIgnoreCase(brand.getEquipmentCategoryName()) && brandName.equalsIgnoreCase(brand.getName())) {
                    equipmentInfo.setBrandValue(brand.getId());
                    equipmentInfo.setBrandName(brand.getName());
                    isRight = true;
                    break;
                } else {
                    isRight = false;
                }
            }
            // 设备用途,这里取分是系统推送，还是手动起草
            // 如果设备用途不为空，则是手动起草
            if (StringUtils.isNotEmpty(usageType)) {
                // 如果设备用途为“营业”
                if (usageType.equals(Constants.COMMON_STATUS_1)) {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_BUSINESS);
                    equipmentInfo.setEquipmentUsageName("营业");
                }
                // 设备用途为“办公”
                else {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_WORK);
                    equipmentInfo.setEquipmentUsageName("办公");
                }
            }
            // 如果设备用途为空，则是系统推送
            else {
                if (equipmentUsageName.contains("办公") || equipmentUsageName.contains("办公用")) {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_WORK);
                    equipmentInfo.setEquipmentUsageName("办公");
                }
                if (equipmentUsageName.contains("营业") || equipmentUsageName.contains("营业用")) {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_BUSINESS);
                    equipmentInfo.setEquipmentUsageName("营业");
                }
            }

            // 操作系统
            for (SysDictValue sysDictValue : osVersionDictValueList) {
                if (osVersionName.equalsIgnoreCase(sysDictValue.getName())) {
                    equipmentInfo.setOsVersionValue(sysDictValue.getValue());
                    equipmentInfo.setOsVersionName(sysDictValue.getName());
                    break;
                }
            }

            // office版本
            for (SysDictValue sysDictValue : officeVersionDictValueList) {
                if (officeVersionName.equalsIgnoreCase(sysDictValue.getName())) {
                    equipmentInfo.setOfficeVersionValue(sysDictValue.getValue());
                    equipmentInfo.setOfficeVersionName(sysDictValue.getName());
                    break;
                }
            }
            equipmentInfoList.add(equipmentInfo);
            i++;
        }
        if (isRight) {
            objectHashMap.put("equipmentInfoList", equipmentInfoList);
        }
        return objectHashMap;
    }



    /**
     * 构建设备信息集合
     */
    private Map<String, Object> buildListByEquipmentWZInfoWeihu(List<EquipmentWZInfo> equipmentWZInfoList, String usageType, String formId) {
        // 最终要返回的集合
        Map<String, Object> objectHashMap = new HashMap<>();
        // 最终要返回的设备集合
        List<EquipmentInfo> equipmentInfoList = new ArrayList<>();
        EquipmentInfo equipmentInfo = null;
        // 设备用途
        String equipmentUsageName;
        // 设备类型
        String equipmentCategoryName;
        // 品牌名称
        String brandName;
        // 责任人姓名
        String principalName;
        // 责任人OA账号
        String principalUsername;
        // 资产标签号
        String assetTagNum;
        // 当前登录人
        String currentUserName = SecurityUtils.getCurrentUserName();
        // 是否为有效数字
        Pattern pattern = Pattern.compile("^[0-9]+(.[0-9]+)?$");
        String patternStr = pattern.toString();

        /**
         * 责任部门信息
         */
        Map<String, Object> map = this.findResponsibleOrg();
        String belongCompanyCode = (String) map.get("belongCompanyCode"); // 负责公司
        String belongCompanyName = (String) map.get("belongCompanyName"); // 负责公司
        String belongDepartmentCode = (String) map.get("belongDepartmentCode"); // 负责部门
        String belongDepartmentName = (String) map.get("belongDepartmentName"); // 负责部门
        String responsibleDepartmentDisplayName = (String) map.get("responsibleDisplayName"); // 负责部门
        String isAdministratorDepartment = (String) map.get("isAdministratorDepartment"); // 负责部门

        // 查询设备类型
        List<EquipmentCategory> categoryList = equipmentCategoryService.findCategoryList(EquipmentCategory.builder().type(Constants.COMMON_STATUS_1).build());
        // 查询设备品牌
        List<EquipmentBrand> brandList = equipmentBrandService.findBrandList(EquipmentBrand.builder().type(Constants.COMMON_STATUS_1).build());
        // 查询未流转下一步，数据库已有的设备列表
        List<EquipmentInfo> infoList = equipmentInfoRepository.queryListByAssetTagNumAndFormId();

        // 如果数据为空
        if (equipmentWZInfoList.isEmpty()) {
            objectHashMap.put("messageContent", "excel数据校验不通过，请检查相关数据!");
            objectHashMap.put("equipmentInfoList", null);
            return map;
        }
//        // 去掉第一行表头数据
//        equipmentWZInfoList.remove(0);
        // 存放资产标签号数据
        List<String> tagNumArray = new ArrayList<>();
        // 数据是否满足
        boolean isRight = true;
        // 行数
        int i = 3;
        for (EquipmentWZInfo equipmentWZInfo : equipmentWZInfoList) {
            equipmentUsageName = equipmentWZInfo.getEquipmentUsageName(); // 设备用途
            equipmentCategoryName = equipmentWZInfo.getEquipmentCategoryName(); // 设备类型
            brandName = equipmentWZInfo.getBrandName(); // 品牌
            principalName = equipmentWZInfo.getPrincipalName(); // 责任人姓名
            principalUsername = equipmentWZInfo.getPrincipalUsername();// 责任人OA账号
            assetTagNum = equipmentWZInfo.getAssetTagNum();// 资产标签号
            equipmentInfo = new EquipmentInfo();

            /**
             * 初始数据
             */
            //  如果工单id不为空
            if (StringUtils.isNotEmpty(formId)) {
                equipmentInfo.setApplicationFormId(formId);
            }
            equipmentInfo.setEquipmentType(Constants.COMMON_STATUS_1);// 设备类型→外置设备
            equipmentInfo.setUsageState(Constants.COMMON_STATUS_ENABLED);// 设备使用状态→默认为正常
            //0 导入状态，1 审批中状态，2 生效状态
            equipmentInfo.setStatus(Constants.COMMON_STATUS_DRAFT);// 设备状态→导入时为 0 导入状态

            /**
             * 设备基础信息
             */
            equipmentInfo.setAssetTagNum(equipmentWZInfo.getAssetTagNum());// 资产标签号
            equipmentInfo.setEquipmentName(equipmentWZInfo.getEquipmentName());

            /**
             * 设备配置信息
             */
            equipmentInfo.setItemModel(equipmentWZInfo.getItemModel());// 规格型号

            /**
             * 归属信息
             */
            equipmentInfo.setPrincipalName(equipmentWZInfo.getPrincipalName());// 责任人姓名
            equipmentInfo.setPrincipalUsername(equipmentWZInfo.getPrincipalUsername());// 责任人OA账号
            /**
             * 责任部门相关处理
             */
            if(StringUtil.isNotBlank(equipmentInfo.getPrincipalUsername())){
                IUser user = uumsSysUserinfoApi.findByKey(equipmentInfo.getPrincipalUsername(), IAuthService.KeyType.username, Constants.APP_CODE); //审批人
               if("03".equals(user.getBelongCompanyTypeDictValue())){
                   equipmentInfo.setResponsibleCompanyCode(user.getBelongCompanyCodeParent());
                   equipmentInfo.setResponsibleCompanyName(user.getBelongCompanyNameParent());
                   equipmentInfo.setResponsibleDepartmentCode(user.getBelongCompanyCode());
                   equipmentInfo.setResponsibleDepartmentName(user.getBelongCompanyName());
                   equipmentInfo.setResponsibleDisplayName(equipmentInfo.getResponsibleCompanyName()+"\\"+equipmentInfo.getResponsibleDepartmentName());
               }else{
                   equipmentInfo.setResponsibleCompanyCode(user.getBelongCompanyCode());
                   equipmentInfo.setResponsibleCompanyName(user.getBelongCompanyName());
                   equipmentInfo.setResponsibleDepartmentCode(user.getBelongDepartmentCode());
                   equipmentInfo.setResponsibleDepartmentName(user.getBelongDepartmentName());
                   equipmentInfo.setResponsibleDisplayName(equipmentInfo.getResponsibleCompanyName()+"\\"+equipmentInfo.getResponsibleDepartmentName());
               }

            }
            /**
             * 设备其他信息
             */
            equipmentInfo.setEquipmentPrice(equipmentWZInfo.getEquipmentPrice());// 设备价格
            equipmentInfo.setEquipmentSite(equipmentWZInfo.getEquipmentSite());// 设备地点名称
            equipmentInfo.setRemark(equipmentWZInfo.getRemark());// 备注
            equipmentInfo.setStatus(2);
            equipmentInfo.setUsageState(1);

            // 分类
            for (EquipmentCategory category : categoryList) {
                if (equipmentCategoryName.equalsIgnoreCase(category.getName())) {
                    equipmentInfo.setEquipmentCategoryValue(category.getId());
                    equipmentInfo.setEquipmentCategoryName(category.getName());
                    isRight = true;
                    break;
                } else {
                    isRight = false;
                }
            }
            // 品牌
            for (EquipmentBrand brand : brandList) {
                if (equipmentCategoryName.equalsIgnoreCase(brand.getEquipmentCategoryName()) && brandName.equalsIgnoreCase(brand.getName())) {
                    equipmentInfo.setBrandValue(brand.getId());
                    equipmentInfo.setBrandName(brand.getName());
                    isRight = true;
                    break;
                } else {
                    isRight = false;
                }
            }

            // 设备用途,这里取分是系统推送，还是手动起草
            // 如果设备用途不为空，则是手动起草
            if (StringUtils.isNotEmpty(usageType)) {
                // 如果设备用途为“营业”
                if (usageType.equals(Constants.COMMON_STATUS_1)) {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_BUSINESS);
                    equipmentInfo.setEquipmentUsageName("营业");
                }
                // 设备用途为“办公”
                else {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_WORK);
                    equipmentInfo.setEquipmentUsageName("办公");
                }
            }
            // 如果设备用途为空，则是系统推送
            else {
                if (equipmentUsageName.contains("办公") || equipmentUsageName.contains("办公用")) {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_WORK);
                    equipmentInfo.setEquipmentUsageName("办公");
                }
                if (equipmentUsageName.contains("营业") || equipmentUsageName.contains("营业用")) {
                    equipmentInfo.setEquipmentUsageValue(Constants.DICT_VALUE_EQUIPMENTUSAGE_BUSINESS);
                    equipmentInfo.setEquipmentUsageName("营业");
                }
            }
            equipmentInfoList.add(equipmentInfo);
            i++;
        }
        if (isRight) {
            objectHashMap.put("equipmentInfoList", equipmentInfoList);
        }
        return objectHashMap;
    }


}
