package com.simbest.boot.nsbgl.equipment.web;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.nsbgl.equipment.service.IEquipmentInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 资产数据同步控制器
 * 用于测试和管理资产数据同步功能
 */
@Api(description = "资产数据同步管理", tags = {"资产数据同步管理"})
@Slf4j
@RestController
@RequestMapping("/asset-sync")
public class AssetSyncController {

    @Autowired
    private IEquipmentInfoService equipmentInfoService;

    /**
     * 同步资产数据到设备信息（简单版本）
     */
    @ApiOperation(value = "同步资产数据到设备信息", notes = "从资产管理系统同步数据到设备信息表")
    @PostMapping("/sync")
    public JsonResponse syncAssetData(
            @ApiParam(value = "当前页", required = true) @RequestParam Integer currentPage,
            @ApiParam(value = "页大小", required = true) @RequestParam Integer pageSize,
            @ApiParam(value = "省份代码", required = true) @RequestParam String provinceCode) {
        
        log.info("开始同步资产数据，页码：{}, 页大小：{}, 省份代码：{}", currentPage, pageSize, provinceCode);
        
        try {
            return equipmentInfoService.syncAssetDataToEquipment(currentPage, pageSize, provinceCode);
        } catch (Exception e) {
            log.error("同步资产数据失败", e);
            return JsonResponse.fail("同步资产数据失败: " + e.getMessage());
        }
    }

    /**
     * 同步资产数据到设备信息（完整参数版本）
     */
    @ApiOperation(value = "同步资产数据到设备信息（完整参数）", notes = "使用完整参数从资产管理系统同步数据")
    @PostMapping("/sync-full")
    public JsonResponse syncAssetDataFull(
            @ApiParam(value = "当前页", required = true) @RequestParam Integer currentPage,
            @ApiParam(value = "页大小", required = true) @RequestParam Integer pageSize,
            @ApiParam(value = "总记录数") @RequestParam(required = false) Integer totalRecord,
            @ApiParam(value = "省份代码", required = true) @RequestParam String provinceCode,
            @ApiParam(value = "责任部门代码") @RequestParam(required = false) String dutyDeptCode,
            @ApiParam(value = "责任人代码") @RequestParam(required = false) String dutyCode,
            @ApiParam(value = "资产类别") @RequestParam(required = false) String assetCategory,
            @ApiParam(value = "资产种类") @RequestParam(required = false) String assetKind,
            @ApiParam(value = "条码") @RequestParam(required = false) String barCode,
            @ApiParam(value = "地址代码") @RequestParam(required = false) String addressCode,
            @ApiParam(value = "状态名称") @RequestParam(required = false) String statusName,
            @ApiParam(value = "是否离职") @RequestParam(required = false) String isLeave) {
        
        log.info("开始完整参数同步资产数据，页码：{}, 页大小：{}, 省份代码：{}", currentPage, pageSize, provinceCode);
        
        try {
            return equipmentInfoService.syncAssetDataToEquipmentFull(
                currentPage, pageSize, totalRecord, provinceCode, dutyDeptCode, dutyCode,
                assetCategory, assetKind, barCode, addressCode, statusName, isLeave);
        } catch (Exception e) {
            log.error("完整参数同步资产数据失败", e);
            return JsonResponse.fail("同步资产数据失败: " + e.getMessage());
        }
    }

    /**
     * 批量同步所有资产数据
     */
    @ApiOperation(value = "批量同步所有资产数据", notes = "批量同步指定省份的所有资产数据到设备信息表")
    @PostMapping("/sync-all")
    public JsonResponse syncAllAssetData(
            @ApiParam(value = "省份代码", required = true) @RequestParam String provinceCode,
            @ApiParam(value = "最大页数", required = false, defaultValue = "100") @RequestParam(required = false, defaultValue = "100") Integer maxPages) {
        
        log.info("开始批量同步所有资产数据，省份代码：{}, 最大页数：{}", provinceCode, maxPages);
        
        try {
            return equipmentInfoService.syncAllAssetDataToEquipment(provinceCode, maxPages);
        } catch (Exception e) {
            log.error("批量同步资产数据失败", e);
            return JsonResponse.fail("批量同步资产数据失败: " + e.getMessage());
        }
    }

    /**
     * 测试资产查询接口连通性
     */
    @ApiOperation(value = "测试资产查询接口", notes = "测试与资产管理系统的连通性")
    @GetMapping("/test")
    public JsonResponse testAssetQuery(
            @ApiParam(value = "省份代码", required = true) @RequestParam String provinceCode) {
        
        log.info("测试资产查询接口，省份代码：{}", provinceCode);
        
        try {
            // 只查询第一页的少量数据进行测试
            return equipmentInfoService.syncAssetDataToEquipment(1, 5, provinceCode);
        } catch (Exception e) {
            log.error("测试资产查询接口失败", e);
            return JsonResponse.fail("测试失败: " + e.getMessage());
        }
    }
}
