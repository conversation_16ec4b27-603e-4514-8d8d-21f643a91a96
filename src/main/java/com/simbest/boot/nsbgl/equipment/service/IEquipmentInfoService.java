package com.simbest.boot.nsbgl.equipment.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.nsbgl.equipment.model.EquipmentAlteration;
import com.simbest.boot.nsbgl.equipment.model.EquipmentInfo;
import org.springframework.data.repository.query.Param;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @description: 设备信息业务接口
 * @author: jing<PERSON><PERSON>
 * @date: 2019/3/8 16:02
 * @version: 1.0
 */
public interface IEquipmentInfoService extends ILogicService<EquipmentInfo, String> {

    /**
     * 查询设备列表
     *
     * @param page          当前页码
     * @param rows          每页数量
     * @param direction     排序规则（asc/desc）
     * @param properties    排序规则（属性名称）
     * @param equipmentInfo 设备信息
     * @return
     */
    JsonResponse findByAll(int page, int rows, String direction, String properties, EquipmentInfo equipmentInfo);


    /**
     * 新增设备信息
     *
     * @param form
     * @return
     */
    JsonResponse createEquipmentInfo(EquipmentInfo form);

    /**
     * 导入设备信息
     *
     * @return
     * @throws Exception
     */
    void importExcel(HttpServletRequest request, HttpServletResponse response);

    /**
     * 保存导入的设备信息
     *
     * @return
     * @throws Exception
     */
    JsonResponse saveEquipmentInfoList(EquipmentInfo equipmentInfo);


    /**
     * 更新设备信息
     *
     * @return
     */
    boolean updateEquipmentList(String formId, String equipmentType, String usageType);


    /**
     * 根据新设备信息，变更原设备信息
     *
     * @param id 设备信息变更工单id
     * @return
     */
    boolean updateEquipmentList(String id);

    /**
     * 更新设备信息(设备录入状态为已生效)
     *
     * @param id
     * @param commonStatusEfficient
     * @return
     */
    boolean updateStatus(String id, Integer commonStatusEfficient);

    /**
     * 查询责任部门信息
     *
     * @return
     */
    Map<String, Object> findResponsibleOrg();


    /**
     * 查询责任部门信息
     *
     * @return
     */
    Map<String, Object> findResponsibleOrg(String source,String username);
    /**
     * 导出设备信息
     * @param equipmentInfo
     * @param response
     * @param request
     * @param sheetName
     * @return
     */
    boolean exportExcel(EquipmentInfo equipmentInfo, HttpServletResponse response, HttpServletRequest request, String sheetName);

    /**
     * 更新设备信息
     *
     * @param form
     * @return
     */
    JsonResponse updateEquipmentInfo(EquipmentInfo form);

    /**
     * 根据资产编号查询
     * @param assetTagNum
     * @return
     */
    List<EquipmentInfo> findEnumentInfo(String assetTagNum);






    //  维护功能使用
    /**
     *
     *
     * 查询设备列表
     *维护功能使用
     * @param page          当前页码
     * @param rows          每页数量
     * @param direction     排序规则（asc/desc）
     * @param properties    排序规则（属性名称）
     * @param equipmentInfo 设备信息
     * @return
     */
    JsonResponse findByAllEqument(int page, int rows, String direction, String properties, EquipmentInfo equipmentInfo);


    /**
     * 新增设备信息
     *维护功能使用
     * @param form
     * @return
     */
    JsonResponse createEquipment(EquipmentInfo form);

    /**
     * 更新设备信息
     *
     * @param form
     * @return
     */
    JsonResponse updateEquipment(EquipmentInfo form);

    /**
     * 导入设备信息
     *
     * @return
     * @throws Exception
     */
    void importExcelWeihu(HttpServletRequest request, HttpServletResponse response);

    /**
     * 保存导入的设备信息
     *
     * @return
     * @throws Exception
     */
    JsonResponse saveEquipmentInfoListWeihu(EquipmentInfo equipmentInfo);


}
