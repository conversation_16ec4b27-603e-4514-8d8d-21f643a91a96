package com.simbest.boot.nsbgl.equipment.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @description: 设备管理-电脑终端设备信息
 * @author: jing<PERSON><PERSON>
 * @date: 2019/3/8 16:02
 * @version: 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "us_equipment_info")
@ApiModel(value = "设备管理-电脑终端设备信息")
public class EquipmentInfo extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UEI") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    private String applicationFormId; //  关联设备起草工单ID

    // TODO 关联设备报修起草工单ID

    /**
     * 设备基础信息
     */
    @Column(length = 100)
    @ApiModelProperty(value = "设备唯一编号")
    @ExcelVOAttribute(name = "设备编号", column = "A")
    private String uniqueNum; //设备唯一编号，系统分配，DN-20190101-00001、DN-20190101-00002

    @Column(length = 200)
    @ApiModelProperty(value = "资产标签号")
    @ExcelVOAttribute(name = "资产标签号", column = "C")
    private String assetTagNum; //资产标签号（参照资产管理系统）

    @Column(length = 40)
    @ApiModelProperty(value = "品牌")
    private String brandValue; //品牌

    @Column(length = 100)
    @ApiModelProperty(value = "品牌")
    @ExcelVOAttribute(name = "品牌", column = "D")
    private String brandName; //品牌

    @Column(length = 40)
    @ApiModelProperty(value = "设备类型")
    private String equipmentCategoryValue; //设备类型，下拉框

    @Column(length = 100)
    @ApiModelProperty(value = "设备类型")
    @ExcelVOAttribute(name = "设备类型", column = "R")
    private String equipmentCategoryName; //设备类型，下拉框

    @Column(length = 40)
    @ApiModelProperty(value = "设备用途")
    private String equipmentUsageValue; //设备用途，下拉框

    @Column(length = 100)
    @ApiModelProperty(value = "设备用途")
    @ExcelVOAttribute(name = "设备用途", column = "S")
    private String equipmentUsageName; //设备用途，下拉框

    @Column(length = 100)
    @ApiModelProperty(value = "设备名称")
    @ExcelVOAttribute(name = "设备名称", column = "B")
    private String equipmentName; //设备名称,必须包含中文

    /**
     * 设备配置信息
     */
    @Column(nullable = false, length = 500)
    @ApiModelProperty(value = "规格型号")
    @ExcelVOAttribute(name = "规格型号", column = "E")
    private String itemModel; //规格型号

    @Column(length = 100)
    @ApiModelProperty(value = "内存")
    @ExcelVOAttribute(name = "内存", column = "F")
    private String memory; //内存，电脑终端设备相关字段

    @Column(length = 100)
    @ApiModelProperty(value = "硬盘容量")
    @ExcelVOAttribute(name = "硬盘容量", column = "G")
    private String diskCapacity; //硬盘容量，电脑终端设备相关字段

    /**
     * 设备软件版本信息（电脑终端设备相关字段）
     */
    @Column(length = 40)
    @ApiModelProperty(value = "操作系统")
    private String osVersionValue; //操作系统，下拉框

    @Column(length = 100)
    @ApiModelProperty(value = "操作系统")
    @ExcelVOAttribute(name = "操作系统", column = "H")
    private String osVersionName; //操作系统，下拉框

    @Column(length = 40)
    @ApiModelProperty(value = "OFFICE版本")
    private String officeVersionValue; //OFFICE版本，下拉框

    @Column(length = 100)
    @ApiModelProperty(value = "OFFICE版本")
    @ExcelVOAttribute(name = "OFFICE版本", column = "I")
    private String officeVersionName; //OFFICE版本，下拉框

    /**
     * 日期信息
     */
    @Column(nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "设备生产日期")
    @ExcelVOAttribute(name = "设备生产日期", column = "K")
    private LocalDate producedDate; //设备生产日期

    @Column(nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始启用日期")
    @ExcelVOAttribute(name = "开始启用日期", column = "J")
    private LocalDate startUsingDate; //开始启用日期（参照资产管理系统）

    /**
     * 归属信息
     */

    @Column(length = 100)
    private String responsibleCompanyCode; //责任公司

    @Column(length = 100)
    private String responsibleCompanyName; //责任公司

    @Column(length = 100)
    private String responsibleDepartmentCode; //责任部门

    @Column(length = 100)
    private String responsibleDepartmentName; //责任部门

    //@Column(length = 100)
    //private String responsibleOrgCode; //责任部门组织
    //
    //@Column(length = 100)
    //private String responsibleOrgName; //责任部门组织

    @Column(length = 100)
    @ExcelVOAttribute(name = "责任部门", column = "M")
    private String responsibleDisplayName; //责任部门

    @Column(length = 100)
    @ApiModelProperty(value = "责任人姓名")
    @ExcelVOAttribute(name = "责任人姓名", column = "N")
    private String principalName; //责任人姓名

    @Column(length = 100)
    @ApiModelProperty(value = "责任人OA账号")
    @ExcelVOAttribute(name = "责任人OA账号", column = "O")
    private String principalUsername; //责任人OA账号

    /**
     * 维保信息
     */
    @Column
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "免费维保结束日期")
    @ExcelVOAttribute(name = "免费维保结束日期", column = "U")
    private LocalDate maintenanceEndDate; //免费维保结束日期

    /**
     * 设备其他信息
     */
    @Column(length = 100)
    @ApiModelProperty(value = "设备价格")
    @ExcelVOAttribute(name = "设备价格(万元)", column = "V")
    private String equipmentPrice; //设备价格

    @Column(length = 500)
    @ApiModelProperty(value = "设备地点名称")
    @ExcelVOAttribute(name = "设备地点名称", column = "T")
    private String equipmentSite; //设备地点名称（参照资产管理系统）

    @Column(length = 500)
    @ApiModelProperty(value = "备注")
    @ExcelVOAttribute(name = "备注", column = "W")
    private String remark; //备注

    @Column(length = 2)
    @ApiModelProperty(value = "设备状态")
    private Integer usageState; //设备使用状态 ，1正常/ 0报废

    @Column(length = 50)
    @ApiModelProperty(value = "设备使用年限")
    private String equipmentUsageLife; //设备使用年限,根据设备启用日期自动计算

    @Column(length = 2)
    @ApiModelProperty(value = "设备类型")
    private String equipmentType; // 0 代表 电脑终端设备，1 代表外置设备

    // 设备三种状态 导入状态，0 导入状态，1 审批中状态，2 生效状态
    @Column(length = 2)
    @ApiModelProperty(value = "设备录入状态")
    private Integer status; //0 导入状态，1 审批中状态，2 生效状态

    @Transient
    @ExcelVOAttribute(name = "登记人OA账号", column = "P")
    private String creatorUser; // 登记人OA账号

    @Transient
    @ExcelVOAttribute(name = "设备登记日期", column = "L")
    private String createdDateTime; // 设备登记日期

    @Transient
    @ExcelVOAttribute(name = "设备状态", column = "Q")
    private String usageStatusStr; // 设备状态

    @Transient
    private List<EquipmentInfo> equipmentInfoList; //电脑终端设备信息列表

    @Transient
    private String queryMode;// 查询设备列表方式，这里取分“设备录入查询”和“设备生效之后的列表查询”，0 ：设备录入查询，1：设备生效之后的列表查询

    //@Transient
    //private String isMySelf; // 是否本人登记

    // TODO 关联设备维修记录

}
