package com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 3.5.0
 * 2025-03-04T16:19:48.456+08:00
 * Generated source version: 3.5.0
 *
 */
@WebService(targetNamespace = "http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv", name = "OSB_CEAM_ZC_HQ_PageInquiryAssetSrv")
@XmlSeeAlso({com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.ObjectFactory.class, ObjectFactory.class})
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface OSBCEAMZCHQPageInquiryAssetSrv {

    @WebMethod(action = "process")
    @WebResult(name = "OutputParameters", targetNamespace = "http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv", partName = "payload")
    public com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OutputParameters process(

        @WebParam(partName = "payload", name = "InputParameters", targetNamespace = "http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv")
        InputParameters payload
    );
}
