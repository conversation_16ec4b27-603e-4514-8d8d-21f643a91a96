
package com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _InputParameters_QNAME = new QName("http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv", "InputParameters");
    private final static QName _OutputParameters_QNAME = new QName("http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv", "OutputParameters");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link InputParameters }
     * 
     */
    public InputParameters createInputParameters() {
        return new InputParameters();
    }

    /**
     * Create an instance of {@link com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OutputParameters }
     * 
     */
    public com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OutputParameters createOutputParameters() {
        return new com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OutputParameters();
    }

    /**
     * Create an instance of {@link com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OUTPUTCOLLECTIONITEM }
     * 
     */
    public com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OUTPUTCOLLECTIONITEM createOUTPUTCOLLECTIONITEM() {
        return new com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OUTPUTCOLLECTIONITEM();
    }

    /**
     * Create an instance of {@link com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OUTPUTCOLLECTION }
     * 
     */
    public com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OUTPUTCOLLECTION createOUTPUTCOLLECTION() {
        return new com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OUTPUTCOLLECTION();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link InputParameters }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link InputParameters }{@code >}
     */
    @XmlElementDecl(namespace = "http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv", name = "InputParameters")
    public JAXBElement<InputParameters> createInputParameters(InputParameters value) {
        return new JAXBElement<InputParameters>(_InputParameters_QNAME, InputParameters.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OutputParameters }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OutputParameters }{@code >}
     */
    @XmlElementDecl(namespace = "http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv", name = "OutputParameters")
    public JAXBElement<com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OutputParameters> createOutputParameters(com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OutputParameters value) {
        return new JAXBElement<com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OutputParameters>(_OutputParameters_QNAME, com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OutputParameters.class, null, value);
    }

}
