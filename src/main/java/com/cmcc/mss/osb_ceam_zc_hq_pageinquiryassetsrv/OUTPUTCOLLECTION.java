
package com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>OUTPUTCOLLECTION complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="OUTPUTCOLLECTION"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="OUTPUTCOLLECTION_ITEM" type="{http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv}OUTPUTCOLLECTION_ITEM" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OUTPUTCOLLECTION", propOrder = {
    "outputcollectionitem"
})
public class OUTPUTCOLLECTION {

    @XmlElement(name = "OUTPUTCOLLECTION_ITEM")
    protected List<com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OUTPUTCOLLECTIONITEM> outputcollectionitem;

    /**
     * Gets the value of the outputcollectionitem property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the outputcollectionitem property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getOUTPUTCOLLECTIONITEM().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OUTPUTCOLLECTIONITEM }
     * 
     * 
     */
    public List<com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OUTPUTCOLLECTIONITEM> getOUTPUTCOLLECTIONITEM() {
        if (outputcollectionitem == null) {
            outputcollectionitem = new ArrayList<com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OUTPUTCOLLECTIONITEM>();
        }
        return this.outputcollectionitem;
    }

}
