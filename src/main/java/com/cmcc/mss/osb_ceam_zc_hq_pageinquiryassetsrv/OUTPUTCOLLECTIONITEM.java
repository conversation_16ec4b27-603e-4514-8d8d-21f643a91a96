
package com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv;

import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>OUTPUTCOLLECTION_ITEM complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="OUTPUTCOLLECTION_ITEM"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="BOOK_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ASSET_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="BAR_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ASSET_CATEGORY" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ASSET_KIND" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ASSET_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="REMARKS" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="TYPE_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="TYPE_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="DUTY_DEPT_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="DUTY_DEPT_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="DUTY_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="DUTY_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="USE_DEPT_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="USE_DEPT_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="USER_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="USER_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MANAGE_DEPT_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MANAGE_DEPT_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ADDRESS_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ADDRESS_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PRODUCTOR" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PROVIDER_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="MODEL" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="AMOUNT" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ITEM_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="STATUS_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ASSET_LOCK" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IS_LEAVE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ASSET_ORI_VALUE" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="ASSET_SALVAGE_VALUE" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="YEAR_DEPR" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="TOTAL_DEPR" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="TOTAL_REDUCE" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="RES_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="UPDATED_ON" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ASSET_NET_VALUE" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="ATTRIBUTE1" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTRIBUTE2" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTRIBUTE3" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTRIBUTE4" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTRIBUTE5" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTRIBUTE6" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTRIBUTE7" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTRIBUTE8" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTRIBUTE9" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ATTRIBUTE10" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="OUTPUT_EXT" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OUTPUTCOLLECTION_ITEM", propOrder = {
    "bookcode",
    "assetcode",
    "barcode",
    "assetcategory",
    "assetkind",
    "assetname",
    "remarks",
    "typecode",
    "typename",
    "dutydeptcode",
    "dutydeptname",
    "dutycode",
    "dutyname",
    "usedeptcode",
    "usedeptname",
    "usercode",
    "username",
    "managedeptcode",
    "managedeptname",
    "addresscode",
    "addressname",
    "productor",
    "providername",
    "model",
    "amount",
    "itemcode",
    "statusname",
    "assetlock",
    "isleave",
    "assetorivalue",
    "assetsalvagevalue",
    "yeardepr",
    "totaldepr",
    "totalreduce",
    "restype",
    "updatedon",
    "assetnetvalue",
    "attribute1",
    "attribute2",
    "attribute3",
    "attribute4",
    "attribute5",
    "attribute6",
    "attribute7",
    "attribute8",
    "attribute9",
    "attribute10",
    "outputext"
})
public class OUTPUTCOLLECTIONITEM {

    @XmlElement(name = "BOOK_CODE", required = true, nillable = true)
    protected String bookcode;
    @XmlElement(name = "ASSET_CODE", required = true, nillable = true)
    protected String assetcode;
    @XmlElement(name = "BAR_CODE", required = true, nillable = true)
    protected String barcode;
    @XmlElement(name = "ASSET_CATEGORY", required = true, nillable = true)
    protected String assetcategory;
    @XmlElement(name = "ASSET_KIND", required = true, nillable = true)
    protected String assetkind;
    @XmlElement(name = "ASSET_NAME", required = true, nillable = true)
    protected String assetname;
    @XmlElement(name = "REMARKS", required = true, nillable = true)
    protected String remarks;
    @XmlElement(name = "TYPE_CODE", required = true, nillable = true)
    protected String typecode;
    @XmlElement(name = "TYPE_NAME", required = true, nillable = true)
    protected String typename;
    @XmlElement(name = "DUTY_DEPT_CODE", required = true, nillable = true)
    protected String dutydeptcode;
    @XmlElement(name = "DUTY_DEPT_NAME", required = true, nillable = true)
    protected String dutydeptname;
    @XmlElement(name = "DUTY_CODE", required = true, nillable = true)
    protected String dutycode;
    @XmlElement(name = "DUTY_NAME", required = true, nillable = true)
    protected String dutyname;
    @XmlElement(name = "USE_DEPT_CODE", required = true, nillable = true)
    protected String usedeptcode;
    @XmlElement(name = "USE_DEPT_NAME", required = true, nillable = true)
    protected String usedeptname;
    @XmlElement(name = "USER_CODE", required = true, nillable = true)
    protected String usercode;
    @XmlElement(name = "USER_NAME", required = true, nillable = true)
    protected String username;
    @XmlElement(name = "MANAGE_DEPT_CODE", required = true, nillable = true)
    protected String managedeptcode;
    @XmlElement(name = "MANAGE_DEPT_NAME", required = true, nillable = true)
    protected String managedeptname;
    @XmlElement(name = "ADDRESS_CODE", required = true, nillable = true)
    protected String addresscode;
    @XmlElement(name = "ADDRESS_NAME", required = true, nillable = true)
    protected String addressname;
    @XmlElement(name = "PRODUCTOR", required = true, nillable = true)
    protected String productor;
    @XmlElement(name = "PROVIDER_NAME", required = true, nillable = true)
    protected String providername;
    @XmlElement(name = "MODEL", required = true, nillable = true)
    protected String model;
    @XmlElement(name = "AMOUNT", required = true, nillable = true)
    protected String amount;
    @XmlElement(name = "ITEM_CODE", required = true, nillable = true)
    protected String itemcode;
    @XmlElement(name = "STATUS_NAME", required = true, nillable = true)
    protected String statusname;
    @XmlElement(name = "ASSET_LOCK", required = true, nillable = true)
    protected String assetlock;
    @XmlElement(name = "IS_LEAVE", required = true, nillable = true)
    protected String isleave;
    @XmlElement(name = "ASSET_ORI_VALUE", required = true, nillable = true)
    protected BigDecimal assetorivalue;
    @XmlElement(name = "ASSET_SALVAGE_VALUE", required = true, nillable = true)
    protected BigDecimal assetsalvagevalue;
    @XmlElement(name = "YEAR_DEPR", required = true, nillable = true)
    protected BigDecimal yeardepr;
    @XmlElement(name = "TOTAL_DEPR", required = true, nillable = true)
    protected BigDecimal totaldepr;
    @XmlElement(name = "TOTAL_REDUCE", required = true, nillable = true)
    protected BigDecimal totalreduce;
    @XmlElement(name = "RES_TYPE", required = true, nillable = true)
    protected String restype;
    @XmlElement(name = "UPDATED_ON", required = true, nillable = true)
    protected String updatedon;
    @XmlElement(name = "ASSET_NET_VALUE", required = true, nillable = true)
    protected BigDecimal assetnetvalue;
    @XmlElement(name = "ATTRIBUTE1", required = true, nillable = true)
    protected String attribute1;
    @XmlElement(name = "ATTRIBUTE2", required = true, nillable = true)
    protected String attribute2;
    @XmlElement(name = "ATTRIBUTE3", required = true, nillable = true)
    protected String attribute3;
    @XmlElement(name = "ATTRIBUTE4", required = true, nillable = true)
    protected String attribute4;
    @XmlElement(name = "ATTRIBUTE5", required = true, nillable = true)
    protected String attribute5;
    @XmlElement(name = "ATTRIBUTE6", required = true, nillable = true)
    protected String attribute6;
    @XmlElement(name = "ATTRIBUTE7", required = true, nillable = true)
    protected String attribute7;
    @XmlElement(name = "ATTRIBUTE8", required = true, nillable = true)
    protected String attribute8;
    @XmlElement(name = "ATTRIBUTE9", required = true, nillable = true)
    protected String attribute9;
    @XmlElement(name = "ATTRIBUTE10", required = true, nillable = true)
    protected String attribute10;
    @XmlElement(name = "OUTPUT_EXT", required = true, nillable = true)
    protected String outputext;

    /**
     * 获取bookcode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBOOKCODE() {
        return bookcode;
    }

    /**
     * 设置bookcode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBOOKCODE(String value) {
        this.bookcode = value;
    }

    /**
     * 获取assetcode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getASSETCODE() {
        return assetcode;
    }

    /**
     * 设置assetcode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setASSETCODE(String value) {
        this.assetcode = value;
    }

    /**
     * 获取barcode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBARCODE() {
        return barcode;
    }

    /**
     * 设置barcode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBARCODE(String value) {
        this.barcode = value;
    }

    /**
     * 获取assetcategory属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getASSETCATEGORY() {
        return assetcategory;
    }

    /**
     * 设置assetcategory属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setASSETCATEGORY(String value) {
        this.assetcategory = value;
    }

    /**
     * 获取assetkind属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getASSETKIND() {
        return assetkind;
    }

    /**
     * 设置assetkind属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setASSETKIND(String value) {
        this.assetkind = value;
    }

    /**
     * 获取assetname属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getASSETNAME() {
        return assetname;
    }

    /**
     * 设置assetname属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setASSETNAME(String value) {
        this.assetname = value;
    }

    /**
     * 获取remarks属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getREMARKS() {
        return remarks;
    }

    /**
     * 设置remarks属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setREMARKS(String value) {
        this.remarks = value;
    }

    /**
     * 获取typecode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTYPECODE() {
        return typecode;
    }

    /**
     * 设置typecode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTYPECODE(String value) {
        this.typecode = value;
    }

    /**
     * 获取typename属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTYPENAME() {
        return typename;
    }

    /**
     * 设置typename属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTYPENAME(String value) {
        this.typename = value;
    }

    /**
     * 获取dutydeptcode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDUTYDEPTCODE() {
        return dutydeptcode;
    }

    /**
     * 设置dutydeptcode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDUTYDEPTCODE(String value) {
        this.dutydeptcode = value;
    }

    /**
     * 获取dutydeptname属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDUTYDEPTNAME() {
        return dutydeptname;
    }

    /**
     * 设置dutydeptname属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDUTYDEPTNAME(String value) {
        this.dutydeptname = value;
    }

    /**
     * 获取dutycode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDUTYCODE() {
        return dutycode;
    }

    /**
     * 设置dutycode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDUTYCODE(String value) {
        this.dutycode = value;
    }

    /**
     * 获取dutyname属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDUTYNAME() {
        return dutyname;
    }

    /**
     * 设置dutyname属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDUTYNAME(String value) {
        this.dutyname = value;
    }

    /**
     * 获取usedeptcode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUSEDEPTCODE() {
        return usedeptcode;
    }

    /**
     * 设置usedeptcode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUSEDEPTCODE(String value) {
        this.usedeptcode = value;
    }

    /**
     * 获取usedeptname属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUSEDEPTNAME() {
        return usedeptname;
    }

    /**
     * 设置usedeptname属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUSEDEPTNAME(String value) {
        this.usedeptname = value;
    }

    /**
     * 获取usercode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUSERCODE() {
        return usercode;
    }

    /**
     * 设置usercode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUSERCODE(String value) {
        this.usercode = value;
    }

    /**
     * 获取username属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUSERNAME() {
        return username;
    }

    /**
     * 设置username属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUSERNAME(String value) {
        this.username = value;
    }

    /**
     * 获取managedeptcode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMANAGEDEPTCODE() {
        return managedeptcode;
    }

    /**
     * 设置managedeptcode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMANAGEDEPTCODE(String value) {
        this.managedeptcode = value;
    }

    /**
     * 获取managedeptname属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMANAGEDEPTNAME() {
        return managedeptname;
    }

    /**
     * 设置managedeptname属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMANAGEDEPTNAME(String value) {
        this.managedeptname = value;
    }

    /**
     * 获取addresscode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getADDRESSCODE() {
        return addresscode;
    }

    /**
     * 设置addresscode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setADDRESSCODE(String value) {
        this.addresscode = value;
    }

    /**
     * 获取addressname属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getADDRESSNAME() {
        return addressname;
    }

    /**
     * 设置addressname属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setADDRESSNAME(String value) {
        this.addressname = value;
    }

    /**
     * 获取productor属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPRODUCTOR() {
        return productor;
    }

    /**
     * 设置productor属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPRODUCTOR(String value) {
        this.productor = value;
    }

    /**
     * 获取providername属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPROVIDERNAME() {
        return providername;
    }

    /**
     * 设置providername属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPROVIDERNAME(String value) {
        this.providername = value;
    }

    /**
     * 获取model属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMODEL() {
        return model;
    }

    /**
     * 设置model属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMODEL(String value) {
        this.model = value;
    }

    /**
     * 获取amount属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAMOUNT() {
        return amount;
    }

    /**
     * 设置amount属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAMOUNT(String value) {
        this.amount = value;
    }

    /**
     * 获取itemcode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getITEMCODE() {
        return itemcode;
    }

    /**
     * 设置itemcode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setITEMCODE(String value) {
        this.itemcode = value;
    }

    /**
     * 获取statusname属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSTATUSNAME() {
        return statusname;
    }

    /**
     * 设置statusname属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSTATUSNAME(String value) {
        this.statusname = value;
    }

    /**
     * 获取assetlock属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getASSETLOCK() {
        return assetlock;
    }

    /**
     * 设置assetlock属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setASSETLOCK(String value) {
        this.assetlock = value;
    }

    /**
     * 获取isleave属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getISLEAVE() {
        return isleave;
    }

    /**
     * 设置isleave属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setISLEAVE(String value) {
        this.isleave = value;
    }

    /**
     * 获取assetorivalue属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getASSETORIVALUE() {
        return assetorivalue;
    }

    /**
     * 设置assetorivalue属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setASSETORIVALUE(BigDecimal value) {
        this.assetorivalue = value;
    }

    /**
     * 获取assetsalvagevalue属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getASSETSALVAGEVALUE() {
        return assetsalvagevalue;
    }

    /**
     * 设置assetsalvagevalue属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setASSETSALVAGEVALUE(BigDecimal value) {
        this.assetsalvagevalue = value;
    }

    /**
     * 获取yeardepr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getYEARDEPR() {
        return yeardepr;
    }

    /**
     * 设置yeardepr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setYEARDEPR(BigDecimal value) {
        this.yeardepr = value;
    }

    /**
     * 获取totaldepr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTOTALDEPR() {
        return totaldepr;
    }

    /**
     * 设置totaldepr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTOTALDEPR(BigDecimal value) {
        this.totaldepr = value;
    }

    /**
     * 获取totalreduce属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTOTALREDUCE() {
        return totalreduce;
    }

    /**
     * 设置totalreduce属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTOTALREDUCE(BigDecimal value) {
        this.totalreduce = value;
    }

    /**
     * 获取restype属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESTYPE() {
        return restype;
    }

    /**
     * 设置restype属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESTYPE(String value) {
        this.restype = value;
    }

    /**
     * 获取updatedon属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUPDATEDON() {
        return updatedon;
    }

    /**
     * 设置updatedon属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUPDATEDON(String value) {
        this.updatedon = value;
    }

    /**
     * 获取assetnetvalue属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getASSETNETVALUE() {
        return assetnetvalue;
    }

    /**
     * 设置assetnetvalue属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setASSETNETVALUE(BigDecimal value) {
        this.assetnetvalue = value;
    }

    /**
     * 获取attribute1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTRIBUTE1() {
        return attribute1;
    }

    /**
     * 设置attribute1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTRIBUTE1(String value) {
        this.attribute1 = value;
    }

    /**
     * 获取attribute2属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTRIBUTE2() {
        return attribute2;
    }

    /**
     * 设置attribute2属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTRIBUTE2(String value) {
        this.attribute2 = value;
    }

    /**
     * 获取attribute3属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTRIBUTE3() {
        return attribute3;
    }

    /**
     * 设置attribute3属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTRIBUTE3(String value) {
        this.attribute3 = value;
    }

    /**
     * 获取attribute4属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTRIBUTE4() {
        return attribute4;
    }

    /**
     * 设置attribute4属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTRIBUTE4(String value) {
        this.attribute4 = value;
    }

    /**
     * 获取attribute5属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTRIBUTE5() {
        return attribute5;
    }

    /**
     * 设置attribute5属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTRIBUTE5(String value) {
        this.attribute5 = value;
    }

    /**
     * 获取attribute6属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTRIBUTE6() {
        return attribute6;
    }

    /**
     * 设置attribute6属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTRIBUTE6(String value) {
        this.attribute6 = value;
    }

    /**
     * 获取attribute7属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTRIBUTE7() {
        return attribute7;
    }

    /**
     * 设置attribute7属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTRIBUTE7(String value) {
        this.attribute7 = value;
    }

    /**
     * 获取attribute8属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTRIBUTE8() {
        return attribute8;
    }

    /**
     * 设置attribute8属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTRIBUTE8(String value) {
        this.attribute8 = value;
    }

    /**
     * 获取attribute9属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTRIBUTE9() {
        return attribute9;
    }

    /**
     * 设置attribute9属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTRIBUTE9(String value) {
        this.attribute9 = value;
    }

    /**
     * 获取attribute10属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getATTRIBUTE10() {
        return attribute10;
    }

    /**
     * 设置attribute10属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setATTRIBUTE10(String value) {
        this.attribute10 = value;
    }

    /**
     * 获取outputext属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOUTPUTEXT() {
        return outputext;
    }

    /**
     * 设置outputext属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOUTPUTEXT(String value) {
        this.outputext = value;
    }

}
