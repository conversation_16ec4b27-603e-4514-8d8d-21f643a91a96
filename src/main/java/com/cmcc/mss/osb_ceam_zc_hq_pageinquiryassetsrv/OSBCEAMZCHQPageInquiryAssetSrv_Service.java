package com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.5.0
 * 2025-03-04T16:19:48.488+08:00
 * Generated source version: 3.5.0
 *
 */
@WebServiceClient(name = "OSB_CEAM_ZC_HQ_PageInquiryAssetSrv",
                  wsdlLocation = "file:/C:/Users/<USER>/Desktop/WSDL文件_20250304100419/OSB_CEAM_ZC_HQ_00005_查询资产信息服务（分页）_V0.1/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv.wsdl",
                  targetNamespace = "http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv")
public class OSBCEAMZCHQPageInquiryAssetSrv_Service extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv", "OSB_CEAM_ZC_HQ_PageInquiryAssetSrv");
    public final static QName OSBCEAMZCHQPageInquiryAssetSrvPort = new QName("http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv", "OSB_CEAM_ZC_HQ_PageInquiryAssetSrvPort");
    static {
        URL url = null;
        try {
            url = new URL("file:/C:/Users/<USER>/Desktop/WSDL文件_20250304100419/OSB_CEAM_ZC_HQ_00005_查询资产信息服务（分页）_V0.1/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv.wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(OSBCEAMZCHQPageInquiryAssetSrv_Service.class.getName())
                .log(java.util.logging.Level.INFO,
                     "Can not initialize the default wsdl from {0}", "file:/C:/Users/<USER>/Desktop/WSDL文件_20250304100419/OSB_CEAM_ZC_HQ_00005_查询资产信息服务（分页）_V0.1/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv.wsdl");
        }
        WSDL_LOCATION = url;
    }

    public OSBCEAMZCHQPageInquiryAssetSrv_Service(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public OSBCEAMZCHQPageInquiryAssetSrv_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public OSBCEAMZCHQPageInquiryAssetSrv_Service() {
        super(WSDL_LOCATION, SERVICE);
    }

    public OSBCEAMZCHQPageInquiryAssetSrv_Service(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public OSBCEAMZCHQPageInquiryAssetSrv_Service(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public OSBCEAMZCHQPageInquiryAssetSrv_Service(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }




    /**
     *
     * @return
     *     returns OSBCEAMZCHQPageInquiryAssetSrv
     */
    @WebEndpoint(name = "OSB_CEAM_ZC_HQ_PageInquiryAssetSrvPort")
    public OSBCEAMZCHQPageInquiryAssetSrv getOSBCEAMZCHQPageInquiryAssetSrvPort() {
        return super.getPort(OSBCEAMZCHQPageInquiryAssetSrvPort, OSBCEAMZCHQPageInquiryAssetSrv.class);
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns OSBCEAMZCHQPageInquiryAssetSrv
     */
    @WebEndpoint(name = "OSB_CEAM_ZC_HQ_PageInquiryAssetSrvPort")
    public OSBCEAMZCHQPageInquiryAssetSrv getOSBCEAMZCHQPageInquiryAssetSrvPort(WebServiceFeature... features) {
        return super.getPort(OSBCEAMZCHQPageInquiryAssetSrvPort, OSBCEAMZCHQPageInquiryAssetSrv.class, features);
    }

}
