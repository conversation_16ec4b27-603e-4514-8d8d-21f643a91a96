package com.simbest.boot.nsbgl.budget.client;

import com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OutputParameters;
import com.cmcc.mss.osb_ceam_zc_hq_pageinquiryassetsrv.OUTPUTCOLLECTIONITEM;
import com.cmcc.mss.sb_fi_bms_pageinquirywxprjmapbgtdptinfosrv.SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvOutputCollection;
import com.cmcc.mss.sb_fi_bms_pageinquirywxprjmapbgtdptinfosrv.SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvOutputItem;
import com.cmcc.mss.sb_fi_bms_pageinquirywxprjmapbgtdptinfosrv.SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvResponse;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.simbest.boot.SimbestApplication;
import com.simbest.boot.nsbgl.budget.model.BudgetMoney;
import com.simbest.boot.nsbgl.budget.model.BudgetMoneyAndDepartment;
import com.simbest.boot.nsbgl.budget.model.ThresholdValue;
import com.simbest.boot.nsbgl.budget.repository.BudgetMoneyAndDepartmentRepository;
import com.simbest.boot.nsbgl.budget.service.IBudgetMoneyService;
import com.simbest.boot.nsbgl.budget.service.IThresholdValueService;
import com.simbest.boot.nsbgl.budget.service.impl.BudgetMoneyServiceImpl;
import com.simbest.boot.nsbgl.budget.service.impl.ThresholdValueServiceImpl;
import com.simbest.boot.nsbgl.util.Constants;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.IteratorUtils;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SimbestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
class BudgetMoneyClientTest {
    @Autowired
    private IThresholdValueService thresholdValueService;
    @Autowired
    private BudgetMoneyAndDepartmentRepository budgetMoneyAndDepartmentRepository;
    @Autowired
    private IBudgetMoneyService budgetMoneyService;
    @Autowired
    private LoginUtils loginUtils;
    @Test
    void test1() {
        loginUtils.adminLogin();
        Boolean flag=false;
        List<BudgetMoneyAndDepartment> budgetMoneyList= Lists.newArrayList();
        // 查询18个分公司组织code，这里可以查询 阈值相关组织code
        Iterable<ThresholdValue> iterable = thresholdValueService.findAllNoPage(Specifications.<ThresholdValue>and()
                .eq("enabled", Constants.COMMON_STATUS_ENABLED) //状态可用
                .build());
        List<ThresholdValue> thresholdValuelist = IteratorUtils.toList(iterable.iterator());
        if (!(thresholdValuelist.size()>0)||thresholdValuelist==null){
            return;
        }
        BudgetMoneyClient budgetMoneyClient=new BudgetMoneyClient();
        //第一次请求,获取总数
        SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvResponse response=budgetMoneyClient.searchBudget(new BigDecimal(1),new BigDecimal(100),new BigDecimal(100));
        BigDecimal totalRecord=response.getTOTALRECORD();
        //第二次请求,通过第一次请求获取到的总数第二次一次获取所有
        SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvResponse rusult=budgetMoneyClient.searchBudget(new BigDecimal(1),totalRecord,totalRecord);
        SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvOutputCollection outputCollection=rusult.getSBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvOutputCollection();
        List<SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvOutputItem> itmeList=outputCollection.getSBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvOutputItem();
        for (SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvOutputItem item : itmeList) {
           budgetMoneyList.add(this.creatBudgetMoneyAndbudgetMoneyAndDepartment(item,thresholdValuelist));
        }
        if (budgetMoneyList.size()>0){
            //获取预算详细数据,准备进行预算同步
            List<BudgetMoney> budgetList = this.budgetMoneyAndDepartmentToBudgetMoney(budgetMoneyList);
            if (budgetList.size()>0){
                Map<String, Object> result=budgetMoneyService.batchUpdateBudget(budgetList);
                if(result.size()>0){
                    flag=true;
                }
            }
          flag= this.saveBudgetMoneyAndDepartment(budgetMoneyList);
        }
        System.out.println("process.result=" + response);
    }

    private Boolean saveBudgetMoneyAndDepartment(List<BudgetMoneyAndDepartment> budgetMoneyList) {
        List<BudgetMoneyAndDepartment> list= budgetMoneyAndDepartmentRepository.findAll();
        if (list.size()>0){
            budgetMoneyAndDepartmentRepository.deleteAllForReally();
        }
        List<BudgetMoneyAndDepartment>   backList=budgetMoneyAndDepartmentRepository.saveAll(budgetMoneyList);
        if (backList.size()>0){
            return true;
        }
        return false;
    }

    private BudgetMoneyAndDepartment creatBudgetMoneyAndbudgetMoneyAndDepartment(SBFIBMSPageInquiryWXPrjMapBgtDptInfoSrvOutputItem item, List<ThresholdValue> thresholdValuelist) {
        BudgetMoneyAndDepartment budgetMoneyAndDepartment = new BudgetMoneyAndDepartment();
        //所属公司和所属部门需要尽心额外处理
        String belongCompanyName=item.getCOMPANYNAME();
        String belongDepartmentName=item.getBUDGETDEPTNAME();
        budgetMoneyAndDepartment.setCreator("hadmin");
        budgetMoneyAndDepartment.setEnabled(true);
        budgetMoneyAndDepartment.setModifier("hadmin");
        budgetMoneyAndDepartment.setBelongCompanyName(belongCompanyName);
        budgetMoneyAndDepartment.setBelongDepartmentName(belongDepartmentName);
        for (ThresholdValue thresholdValue : thresholdValuelist) {
            if (belongCompanyName.equals(thresholdValue.getBelongCompanyName())) {
                budgetMoneyAndDepartment.setBelongCompanyCode(thresholdValue.getBelongCompanyCode());//  所属公司code
                break;
            }
        }
        budgetMoneyAndDepartment.setBudgetNum(item.getPROJECTNUMBER());
        budgetMoneyAndDepartment.setName(item.getPROJECTNAME());
        BigDecimal totalActualB=new BigDecimal(item.getIRESERVED2());
        budgetMoneyAndDepartment.setTotalActualByBS(totalActualB.setScale(2,BigDecimal.ROUND_DOWN).toPlainString());//实际发生金额
        BigDecimal budgetAmountBig = item.getBUDGETAMOUNT();
        budgetMoneyAndDepartment.setTotalAmountByBS(budgetAmountBig.setScale(2,BigDecimal.ROUND_DOWN).toPlainString());//预算金额
        BigDecimal totalLock=new BigDecimal(item.getIRESERVED1());
        budgetMoneyAndDepartment.setTotalLockByBS(totalLock.setScale(2,BigDecimal.ROUND_DOWN).toPlainString());//占用金额
        return budgetMoneyAndDepartment;
    }

    /**
     * 生成预算详情实体类
     * @param budgetMoneyAndDepartment
     */
    private BudgetMoney creatBudgetMoney(BudgetMoneyAndDepartment budgetMoneyAndDepartment) {
        BudgetMoney budgetMoney=new BudgetMoney();
        budgetMoney.setBudgetNum(budgetMoneyAndDepartment.getBudgetNum());
        budgetMoney.setBelongCompanyCode(budgetMoneyAndDepartment.getBelongCompanyCode());
        budgetMoney.setBelongCompanyName(budgetMoneyAndDepartment.getBelongCompanyName());
        budgetMoney.setTotalLockByBS(budgetMoneyAndDepartment.getTotalLockByBS());
        budgetMoney.setTotalAmountByBS(budgetMoneyAndDepartment.getTotalAmountByBS());
        budgetMoney.setTotalActualByBS(budgetMoneyAndDepartment.getTotalActualByBS());
        budgetMoney.setName(budgetMoneyAndDepartment.getName());
        return budgetMoney;
    }

    /**
     * 将关系数据整理为详情数据
     *
     * @param budgetMoneyList
     * @return
     */
    private List<BudgetMoney> budgetMoneyAndDepartmentToBudgetMoney(List<BudgetMoneyAndDepartment> budgetMoneyList) {
        List<BudgetMoney> budgetList = Lists.newArrayList();
        List<String> budgetNumberList = Lists.newArrayList();
        for (BudgetMoneyAndDepartment budgetMoneyAndDepartment : budgetMoneyList) {
            BudgetMoney budgetMoney;
            if (budgetNumberList.size()==0){
                budgetNumberList.add(budgetMoneyAndDepartment.getBudgetNum());
                budgetMoney= this.creatBudgetMoney(budgetMoneyAndDepartment);
                budgetList.add(budgetMoney);
            }else{
                if (!budgetNumberList.contains(budgetMoneyAndDepartment.getBudgetNum())){
                    budgetNumberList.add(budgetMoneyAndDepartment.getBudgetNum());
                    budgetMoney= this.creatBudgetMoney(budgetMoneyAndDepartment);
                    budgetList.add(budgetMoney);
                    continue;
                }
            }
        }
        return budgetList;
    }

    @Test
    void testSearchAsset() {
        loginUtils.adminLogin();
        try {
            // 测试资产查询
            BigDecimal currentPage = new BigDecimal(1);
            BigDecimal pageSize = new BigDecimal(10);
            BigDecimal totalRecord = new BigDecimal(0);
            String provinceCode = "4772276805467173986"; // 使用预算查询中的省份代码

            log.info("开始测试资产查询...");
            OutputParameters response = BudgetMoneyClient.searchAsset(currentPage, pageSize, totalRecord, provinceCode);

            if (response != null) {
                log.info("资产查询成功!");
                log.info("ESB_FLAG: {}", response.getESBFLAG());
                log.info("ESB_RETURN_CODE: {}", response.getESBRETURNCODE());
                log.info("ESB_RETURN_MESSAGE: {}", response.getESBRETURNMESSAGE());
                log.info("BIZ_SERVICE_FLAG: {}", response.getBIZSERVICEFLAG());
                log.info("BIZ_RETURN_CODE: {}", response.getBIZRETURNCODE());
                log.info("BIZ_RETURN_MESSAGE: {}", response.getBIZRETURNMESSAGE());
                log.info("TOTAL_RECORD: {}", response.getTOTALRECORD());
                log.info("TOTAL_PAGE: {}", response.getTOTALPAGE());
                log.info("CURRENT_PAGE: {}", response.getCURRENTPAGE());
                log.info("PAGE_SIZE: {}", response.getPAGESIZE());

                if (response.getOUTPUTCOLLECTION() != null &&
                    response.getOUTPUTCOLLECTION().getOUTPUTCOLLECTIONITEM() != null) {
                    List<OUTPUTCOLLECTIONITEM> items = response.getOUTPUTCOLLECTION().getOUTPUTCOLLECTIONITEM();
                    log.info("返回资产数量: {}", items.size());

                    for (int i = 0; i < Math.min(items.size(), 3); i++) {
                        OUTPUTCOLLECTIONITEM item = items.get(i);
                        log.info("资产{}:", i + 1);
                        log.info("  资产编码: {}", item.getASSETCODE());
                        log.info("  资产名称: {}", item.getASSETNAME());
                        log.info("  条码: {}", item.getBARCODE());
                        log.info("  资产类别: {}", item.getASSETCATEGORY());
                        log.info("  责任部门: {}", item.getDUTYDEPTNAME());
                        log.info("  责任人: {}", item.getDUTYNAME());
                        log.info("  状态: {}", item.getSTATUSNAME());
                        log.info("  原值: {}", item.getASSETORIVALUE());
                        log.info("  净值: {}", item.getASSETNETVALUE());
                    }
                }
            } else {
                log.error("资产查询返回null");
            }
        } catch (Exception e) {
            log.error("资产查询测试异常", e);
        }
    }

}