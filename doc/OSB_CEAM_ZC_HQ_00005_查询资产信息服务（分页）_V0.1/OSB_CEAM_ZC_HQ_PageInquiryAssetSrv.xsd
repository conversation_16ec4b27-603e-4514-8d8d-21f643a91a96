<?xml version = "1.0" encoding = "UTF-8"?>
<!-- 2025-02-19 11:49:40 -->
<!-- Generated file version: V0.13 -->
<!-- Copyright (c) 2004-2025  -->
<schema attributeFormDefault="unqualified"
	elementFormDefault="qualified"
	targetNamespace="http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv"
	xmlns:tns="http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv"
	xmlns:msg="http://soa.cmcc.com/MsgHeader"
	xmlns="http://www.w3.org/2001/XMLSchema">
	<import namespace="http://soa.cmcc.com/MsgHeader" schemaLocation="MsgHeader.xsd"/>

	<element name="InputParameters" type="tns:InputParameters"/>
	<element name="OutputParameters" type="tns:OutputParameters"/>
    
	<complexType name="InputParameters">
     <sequence>
       <element name="MSGHEADER" type="msg:MSGHEADER"  />
       <element name="PROVINCE_CODE" type="string"  />
       <element name="DUTY_DEPT_CODE" type="string" nillable="true" />
       <element name="DUTY_CODE" type="string" nillable="true" />
       <element name="ASSET_CATEGORY" type="string" nillable="true" />
       <element name="ASSET_KIND" type="string" nillable="true" />
       <element name="BAR_CODE" type="string" nillable="true" />
       <element name="ADDRESS_CODE" type="string" nillable="true" />
       <element name="STATUS_NAME" type="string" nillable="true" />
       <element name="IS_LEAVE" type="string" nillable="true" />
       <element name="LAST_UPDATE_START" type="dateTime" nillable="true" />
       <element name="LAST_UPDATE_END" type="dateTime" nillable="true" />
       <element name="ATTRIBUTE1" type="string" nillable="true" />
       <element name="ATTRIBUTE2" type="string" nillable="true" />
       <element name="ATTRIBUTE3" type="string" nillable="true" />
       <element name="ATTRIBUTE4" type="string" nillable="true" />
       <element name="ATTRIBUTE5" type="string" nillable="true" />
       <element name="ATTRIBUTE6" type="string" nillable="true" />
       <element name="ATTRIBUTE7" type="string" nillable="true" />
       <element name="ATTRIBUTE8" type="string" nillable="true" />
       <element name="ATTRIBUTE9" type="string" nillable="true" />
       <element name="ATTRIBUTE10" type="string" nillable="true" />
       <element name="QUERY_EXT" type="string" nillable="true" />
     </sequence>
    </complexType>
     
	<complexType name="OUTPUTCOLLECTION_ITEM">
     <sequence>
        <element name="BOOK_CODE" type="string" nillable="true" />
        <element name="ASSET_CODE" type="string" nillable="true" />
        <element name="BAR_CODE" type="string" nillable="true" />
        <element name="ASSET_CATEGORY" type="string" nillable="true" />
        <element name="ASSET_KIND" type="string" nillable="true" />
        <element name="ASSET_NAME" type="string" nillable="true" />
        <element name="REMARKS" type="string" nillable="true" />
        <element name="TYPE_CODE" type="string" nillable="true" />
        <element name="TYPE_NAME" type="string" nillable="true" />
        <element name="DUTY_DEPT_CODE" type="string" nillable="true" />
        <element name="DUTY_DEPT_NAME" type="string" nillable="true" />
        <element name="DUTY_CODE" type="string" nillable="true" />
        <element name="DUTY_NAME" type="string" nillable="true" />
        <element name="USE_DEPT_CODE" type="string" nillable="true" />
        <element name="USE_DEPT_NAME" type="string" nillable="true" />
        <element name="USER_CODE" type="string" nillable="true" />
        <element name="USER_NAME" type="string" nillable="true" />
        <element name="MANAGE_DEPT_CODE" type="string" nillable="true" />
        <element name="MANAGE_DEPT_NAME" type="string" nillable="true" />
        <element name="ADDRESS_CODE" type="string" nillable="true" />
        <element name="ADDRESS_NAME" type="string" nillable="true" />
        <element name="PRODUCTOR" type="string" nillable="true" />
        <element name="PROVIDER_NAME" type="string" nillable="true" />
        <element name="MODEL" type="string" nillable="true" />
        <element name="AMOUNT" type="string" nillable="true" />
        <element name="ITEM_CODE" type="string" nillable="true" />
        <element name="STATUS_NAME" type="string" nillable="true" />
        <element name="ASSET_LOCK" type="string" nillable="true" />
        <element name="IS_LEAVE" type="string" nillable="true" />
        <element name="ASSET_ORI_VALUE" type="decimal" nillable="true" />
        <element name="ASSET_SALVAGE_VALUE" type="decimal" nillable="true" />
        <element name="YEAR_DEPR" type="decimal" nillable="true" />
        <element name="TOTAL_DEPR" type="decimal" nillable="true" />
        <element name="TOTAL_REDUCE" type="decimal" nillable="true" />
        <element name="RES_TYPE" type="string" nillable="true" />
        <element name="UPDATED_ON" type="string" nillable="true" />
        <element name="ASSET_NET_VALUE" type="decimal" nillable="true" />
        <element name="ATTRIBUTE1" type="string" nillable="true" />
        <element name="ATTRIBUTE2" type="string" nillable="true" />
        <element name="ATTRIBUTE3" type="string" nillable="true" />
        <element name="ATTRIBUTE4" type="string" nillable="true" />
        <element name="ATTRIBUTE5" type="string" nillable="true" />
        <element name="ATTRIBUTE6" type="string" nillable="true" />
        <element name="ATTRIBUTE7" type="string" nillable="true" />
        <element name="ATTRIBUTE8" type="string" nillable="true" />
        <element name="ATTRIBUTE9" type="string" nillable="true" />
        <element name="ATTRIBUTE10" type="string" nillable="true" />
        <element name="OUTPUT_EXT" type="string" nillable="true" />
       </sequence>
    </complexType>
     
	<complexType name="OutputParameters">
     <sequence>
        <element name="ESB_FLAG" type="string" nillable="true" />
        <element name="ESB_RETURN_CODE" type="string" nillable="true" />
        <element name="ESB_RETURN_MESSAGE" type="string" nillable="true" />
        <element name="BIZ_SERVICE_FLAG" type="string" nillable="true" />
        <element name="BIZ_RETURN_CODE" type="string" nillable="true" />
        <element name="BIZ_RETURN_MESSAGE" type="string" nillable="true" />
        <element name="INSTANCE_ID" type="string" nillable="true" />
        <element name="TOTAL_RECORD" type="decimal" nillable="true" />
        <element name="TOTAL_PAGE" type="decimal" nillable="true" />
        <element name="PAGE_SIZE" type="decimal" nillable="true" />
        <element name="CURRENT_PAGE" type="decimal" nillable="true" />
        <element name="OUTPUTCOLLECTION" type="tns:OUTPUTCOLLECTION"  />
       </sequence>
    </complexType>
     
	<complexType name="OUTPUTCOLLECTION">
     <sequence>
        <element name="OUTPUTCOLLECTION_ITEM" type="tns:OUTPUTCOLLECTION_ITEM" minOccurs="0" maxOccurs="unbounded" />
       </sequence>
    </complexType>
</schema>