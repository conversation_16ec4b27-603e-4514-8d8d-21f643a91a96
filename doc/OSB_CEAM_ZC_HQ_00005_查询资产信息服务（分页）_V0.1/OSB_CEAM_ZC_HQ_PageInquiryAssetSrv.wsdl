<?xml version = "1.0" encoding = "UTF-8"?>
<!-- 2025-02-19 11:49:40 -->
<!-- Generated file version: V0.13 -->
<!-- Copyright (c) 2004-2025  -->
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
	xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
	xmlns:tns="http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv"
	name="OSB_CEAM_ZC_HQ_PageInquiryAssetSrv"
	targetNamespace="http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv">
		
	<wsdl:types>
		<schema xmlns="http://www.w3.org/2001/XMLSchema">
			<import namespace="http://soa.cmcc.com/OSB_CEAM_ZC_HQ_PageInquiryAssetSrv" schemaLocation="OSB_CEAM_ZC_HQ_PageInquiryAssetSrv.xsd"/>
		</schema>
	</wsdl:types>
	<wsdl:message name="OSB_CEAM_ZC_HQ_PageInquiryAssetSrvRequestMessage">
		<wsdl:part name="payload" element="tns:InputParameters"/>
	</wsdl:message>
	<wsdl:message name="OSB_CEAM_ZC_HQ_PageInquiryAssetSrvResponseMessage">
		<wsdl:part name="payload" element="tns:OutputParameters"/>
	</wsdl:message>
	<wsdl:portType name="OSB_CEAM_ZC_HQ_PageInquiryAssetSrv">
		<wsdl:operation name="process">
			<wsdl:input message="tns:OSB_CEAM_ZC_HQ_PageInquiryAssetSrvRequestMessage"/>
			<wsdl:output message="tns:OSB_CEAM_ZC_HQ_PageInquiryAssetSrvResponseMessage"/>
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="OSB_CEAM_ZC_HQ_PageInquiryAssetSrvBinding" type="tns:OSB_CEAM_ZC_HQ_PageInquiryAssetSrv">
		<soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
		<wsdl:operation name="process">
			<soap:operation style="document" soapAction="process"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
	 </wsdl:binding>
	<wsdl:service name="OSB_CEAM_ZC_HQ_PageInquiryAssetSrv">
		<wsdl:port name="OSB_CEAM_ZC_HQ_PageInquiryAssetSrvPort" binding="tns:OSB_CEAM_ZC_HQ_PageInquiryAssetSrvBinding">
			<soap:address location=""/>
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>